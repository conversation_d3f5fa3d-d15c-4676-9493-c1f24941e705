import pytest
import requests
import json
from typing import Dict, Any

# Constants
BASE_URL = "http://localhost:8000"
TEST_VIDEO_URL = "https://tx.enn.cn/group1/M00/0B/E9/CiaAUmdg61mAflSHAgLgxNwEGIk888.MP4"
TIMEOUT = 300  # 5 minutes

def test_video_understanding_api_success():
    """Test successful video understanding API call."""
    response = requests.post(
        url=f"{BASE_URL}/video/understanding",
        params={"video_url": TEST_VIDEO_URL},
        timeout=TIMEOUT
    )
    
    assert response.status_code == 200, f"HTTP request failed with status {response.status_code}"
    
    result = response.json()
    assert result.get("code") == 0, f"API returned error: {result.get('message', 'Unknown error')}"
    assert "data" in result, "Response missing 'data' field"
    
    return result["data"]

def test_video_understanding_api_error_handling():
    """Test error handling for video understanding API."""
    # Test with invalid video URL
    invalid_url = "invalid_url"
    
    with pytest.raises(requests.exceptions.RequestException):
        requests.post(
            url=f"{BASE_URL}/video/understanding",
            params={"video_url": invalid_url},
            timeout=TIMEOUT
        )

if __name__ == "__main__":
    try:
        result = test_video_understanding_api_success()
        print("API调用成功！")
        print("返回结果：", json.dumps(result, indent=4, ensure_ascii=False))
    except requests.exceptions.Timeout:
        print("请求超时，请检查服务器状态或增加超时时间")
    except requests.exceptions.ConnectionError:
        print("连接错误，请确保服务器正在运行且地址正确")
    except requests.exceptions.RequestException as e:
        print(f"请求发生错误：{str(e)}")
    except AssertionError as e:
        print(f"测试失败：{str(e)}")