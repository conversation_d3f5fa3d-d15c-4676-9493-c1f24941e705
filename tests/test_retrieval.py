import sys
import os
import pytest
import yaml

# Add project root to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from project import LLMRetrieval, VectorRetrieval

class TestRetrieval:
    @pytest.fixture(autouse=True)
    def setup(self):
        """Set up test fixtures before running tests."""
        llm_retrieval_config_path = "model/Retrieval/llmRetrieval.yaml"
        vector_retrieval_config_path = "model/Retrieval/vectorRetrieval.yaml"

        with open(llm_retrieval_config_path, "r") as f:
            self.llm_retrieval_config = yaml.load(f, Loader=yaml.FullLoader)

        with open(vector_retrieval_config_path, "r") as f:
            self.vector_retrieval_config = yaml.load(f, Loader=yaml.FullLoader)

        self.llm_retrieval = LLMRetrieval(self.llm_retrieval_config)
        self.vector_retrieval = VectorRetrieval(self.vector_retrieval_config)

    def test_retrieval_workflow(self):
        """Test the complete retrieval workflow."""
        # Test vector retrieval
        query = "花"
        result_list = self.vector_retrieval.run(query=query)
        
        # Assert vector retrieval results
        assert isinstance(result_list, list), "Vector retrieval should return a list"
        assert len(result_list) > 0, "Vector retrieval should return non-empty results"
        
        # Test LLM retrieval
        final_result_list = self.llm_retrieval.run(
            video_info_list=result_list,
            retrieval_instruction=query
        )
        
        # Assert LLM retrieval results
        assert isinstance(final_result_list, list), "LLM retrieval should return a list"
        assert len(final_result_list) > 0, "LLM retrieval should return non-empty results"
        
        # Print results for inspection
        print("\nRetrieval Results:")
        print(f"Initial Vector Retrieval Results Count: {len(result_list)}")
        print(f"Final LLM Retrieval Results Count: {len(final_result_list)}")

if __name__ == '__main__':
    pytest.main(["-v", __file__])