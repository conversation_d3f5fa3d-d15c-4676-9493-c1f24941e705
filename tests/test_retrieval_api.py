import pytest
import requests
import json

class TestRetrievalAPI:
    @pytest.fixture(autouse=True)
    def setup(self):
        self.base_url = "http://localhost:8000"
        self.test_data = {
            "query": "美丽的海边风景，碧蓝的大海，洁白的沙滩。",
            "user_limit": 5
        }

    def test_video_retrieval_success(self):
        """Test successful video retrieval API call"""
        response = requests.post(
            url=f"{self.base_url}/video/retrieval",
            json=self.test_data,
            timeout=10000
        )
        
        assert response.status_code == 200, f"API call failed with status code: {response.status_code}"
        
        result = response.json()
        # print(f"result: {result}")
        query = list(result["data"].keys())[0]
        
        # Verify response structure
        assert "data" in result, "Response missing 'data' field"
        assert query in result["data"], f"Query '{query}' not found in response data"
        
        # Verify each result item has required fields
        for item in result["data"][query]:
            assert "video_path" in item, "Result item missing 'video_path'"
            assert "retrieval_score" in item, "Result item missing 'retrieval_score'"
            
            # Print results for debugging
            print(f"Video Path: {item['video_path']}, Retrieval Score: {item['retrieval_score']}")

    def test_video_retrieval_invalid_request(self):
        """Test API behavior with invalid request data"""
        invalid_data = {"invalid_field": "test"}
        
        response = requests.post(
            url=f"{self.base_url}/video/retrieval",
            json=invalid_data,
            timeout=10000
        )
        
        assert response.status_code != 200, "API should reject invalid request data"
        
if __name__ == "__main__":
    pytest.main(["-v", __file__])
    try:
        test_instance = TestRetrievalAPI()
        test_instance.setup()
        test_instance.test_video_retrieval_success()
        test_instance.test_video_retrieval_invalid_request()
    except requests.exceptions.RequestException as e:
        print(f"Request failed: {e}")
    except AssertionError as e:
        print(f"Test failed: {e}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")