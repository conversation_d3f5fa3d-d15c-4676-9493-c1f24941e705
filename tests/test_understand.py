import sys
import os
import pytest
import yaml

# Add project root to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from project import VideoAttributeExtractor

class TestVideoUnderstanding:
    @pytest.fixture(autouse=True)
    def setup(self):
        """Set up test fixtures before running tests."""
        model_config_path = "model/Understand/understand.yaml"
        with open(model_config_path, "r") as f:
            self.model_config = yaml.load(f, Loader=yaml.FullLoader)
        self.video_attribute_extractor = VideoAttributeExtractor(self.model_config)
        self.test_video_path = "data/videos/001.mp4"

    def test_video_understanding(self):
        """Test video understanding functionality."""
        # Run video understanding
        info = self.video_attribute_extractor.run(self.test_video_path)
        
        # Assert that we got a dictionary back
        assert isinstance(info, dict)
        
        # Assert that required keys are present
        required_keys = [
            'video_path', 'width', 'height', 'frame_count', 'fps',
            'duration', 'scene_type', 'scene_info', 'raw_caption',
            'coarse_attributes', 'fine_attributes'
        ]
        for key in required_keys:
            assert key in info, f"Missing required key: {key}"
        
        # Print results for inspection
        print("\nVideo Understanding Results:")
        print(f"Scene Type: {info['scene_type']}")
        print(f"Raw Caption: {info['raw_caption']}")
        print(f"Coarse Attributes: {info['coarse_attributes']}")
        print(f"Fine Attributes: {info['fine_attributes']}")

if __name__ == '__main__':
    pytest.main(["-v", __file__])


