# 项目接口简要说明
本项目包含多个Python模块，旨在实现视频理解、特征提取和基于内容的视频检索功能。以下是项目文件结构和各文件功能的简要介绍。

### 文件结构
```plaintext
project/
├── api/
│   ├── LLM/
│   │   ├── ENNEW.py # 用于连接远程部署的模型
│   │   └── ENNVL.py # 用于连接远程部署的模型
│   ├── Retrieval/
│   │   ├── llmRetrieval.py # 大语言模型深度检索
│   │   └── vectorRetrieval.py # 向量检索
│   └── Understand/
│       ├── attributegen.py  # 视频理解模型
│       ├── audio_tools.py # 提取音频信息
│       ├── grounding_tools.py # 检测显著的事物
│       └── init.py
└── README.md
```

### 文件功能介绍

#### 连接远程部署模型 api/LLM/
项目通过调用远程部署模型的API进行视频理解。项目默认使用**ENNVL**，进行远程连接。
- **ENNEW.py**：定义了与远程部署的模型进行交互的接口。主要函数包括：
  - `get_auth()`: 获取授权令牌。
  - `_generate(messages)`: 基于输入消息生成响应。

- **ENNVL.py**：同样定义了与远程部署的模型进行交互的接口。主要函数包括：
  - `get_auth()`: 获取授权令牌。
  - `_generate(messages)`: 基于输入消息生成响应。

#### 视频理解 api/Understand/

- **attributegen.py**：包含 `VideoAttributeExtractor` 类，用于从视频剪辑中提取属性。其主要功能包含1. 从视频剪辑中提取帧。 2. 使用不同的LLM后端处理帧，默认使用ENNVL 对提取的帧进行理解。3. 处理音频提取，可选项，是否提取视频中的音频进行辅助理解。 4. 生成视频剪辑的属性和描述，按照规定的理解算法对视频进行理解，生成有关视频的场景信息、场景先验、视频主题、粗粒度标签、细粒度标签等信息。

    主要函数包括：
    - `get_attribute_per_clip_huggingface(frame_list, video_clip_info)`: 使用Hugging Face LLM提取剪辑属性。
    - `get_attribute_per_clip_api(frame_list, video_clip_info, llm_type)`: 使用API基于LLM提取剪辑属性。
    - `read_frames(self, ...)`: 从视频文件中提取帧。
    - `_process_prompt_template(self, ...)`: 处理提示模板并更新视频剪辑信息。
    - `run(self, video_path: str, save_path: Optional[str] = None)`: 从视频文件中提取属性。

    以上是 `VideoAttributeExtractor` 类的核心功能介绍。该类通过结合视频帧提取、LLM处理和属性生成，为视频理解任务提供了一套完整的解决方案。

- **audio_tools.py**：提供 `video_to_text` 函数，使用OpenAI的Whisper模型将视频音频转换为文本。

- **grounding_tools.py**：包含 `DetectionClient` 类，用于使用DINO和MLLM模型进行对象检测和图像分析。主要函数包括：
  - `analyze_image_with_gpt4v(image)`: 使用GPT-4V分析图像以识别主要对象。
  - `process_image_with_ai_analysis_and_boxes(image, detect_info_path, targets)`: 处理图像，使用AI分析并绘制边界框。

#### 视频检索 api/Retrieval/

- **llmRetrieval.py**：实现了基于大型语言模型（LLM）的视频片段检索功能。在向量检索的基础上，使用大语言模型进行细粒度的检索，主要函数包括：
  - `get_ennew_langchain_retrieval(user_prompt, system_prompt)`: 使用ENNEW langchain获取检索结果。与大语言模型进行通信，获取更符合用户query的信息。
  - `run(video_info_list, retrieval_instruction)`: 根据描述和指令检索视频片段。返回前topk个，最符合用户描述query的视频。

- **vectorRetrieval.py**：用于配置和执行基于向量的视频检索。主要函数包括：
  - `_create_collection(field_datas)`: 创建Milvus数据集，用户输入query后，从数据集中进行查询符合条件的视频。
  - `_add_records(records, timeout, batch_size, ids)`: 向集合中添加记录。
  - `upsert_records()`: 更新或插入记录。如果记录已存在，则覆盖；否则插入新记录。
  - `delete_records()`: 根据记录的字段值删除指定的记录。
  - `run(query, params, timeout)`: 执行基于向量的检索并返回结果。具体方法为：1.优先使用POI进行排序 2. 对理解后的视频属性内容赋予不同的权重进行排序。

---

以上是项目中各个Python文件的核心功能介绍。这些模块共同工作，以实现一个高效的视频理解和检索系统。