#!/usr/bin/env python
# coding=utf-8

import os
import time
import json
import logging
from typing import Optional
from pathlib import Path

from openai import OpenAI
from moviepy.editor import VideoFileClip
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


def video_to_text(video_path: str, audio_info_path: Optional[str] = None) -> str:
    """
    Convert video audio to text using OpenAI's Whisper model.
    
    Args:
        video_path: Path to the video file
        audio_info_path: Optional path to save/load audio transcription
        
    Returns:
        Transcribed text from the video audio
    """
    # Check if transcription already exists
    if audio_info_path and os.path.exists(audio_info_path):
        logger.debug(f"[Skipping] Loading audio info from {audio_info_path}")
        with open(audio_info_path, 'r') as f:
            return json.load(f)['transcript']
    
    logger.debug("Audio info not found, transcribing audio")
    
    # Initialize OpenAI client
    client = OpenAI(
        api_key=os.environ.get('AUDIO_KEY'),
        base_url=os.environ.get('AUDIO_BASE_URL')
    )
    
    # Extract audio from video
    temp_audio_path = Path(video_path).with_suffix('.mp3')
    if not temp_audio_path.exists():
        video = VideoFileClip(video_path)
        audio = video.audio
        audio.write_audiofile(str(temp_audio_path))
        audio.close()
        video.close()
    
    # Attempt transcription
    for attempt in range(2):
        try:
            with open(temp_audio_path, 'rb') as audio_file:
                transcript = client.audio.transcriptions.create(
                    model="whisper-1",
                    file=audio_file,
                    response_format="text",
                    language="zh"
                )
            
            # Save transcription if path provided
            if audio_info_path:
                json.dump(
                    {"transcript": transcript},
                    open(audio_info_path, 'w'),
                    ensure_ascii=False
                )
            
            return transcript
            
        except Exception as e:
            logger.error(f"Audio transcription failed (attempt {attempt + 1}): {str(e)}")
            if attempt < 1:  # Only sleep if we're going to try again
                time.sleep(2)
    
    # Clean up and return empty string if all attempts failed
    if temp_audio_path.exists():
        temp_audio_path.unlink()
    return ""


if __name__ == "__main__":
    # Example usage
    try:
        video_path = "save/tmp_videos/3ea1977e-6089-48b8-acce-ddbd642da446.mp4"
        text = video_to_text(video_path)
        print("Transcription result:", text)
    except Exception as e:
        print(f"Error: {str(e)}")