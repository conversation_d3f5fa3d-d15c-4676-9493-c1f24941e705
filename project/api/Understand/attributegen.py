import base64
import cv2
import random
import re
import yaml
import json
import copy
import os
import torch
import logging
from typing import List, Dict, Any, Optional, Tuple, Union
from PIL import Image
from openai import OpenAI
from moviepy.editor import VideoFileClip
from langchain_core.messages import HumanMessage, SystemMessage
import numpy as np

from ..LLM.ENNEW import ChatEnnew
from ..LLM.ENNVL import ChatEnnvl
from .audio_tools import video_to_text
from .grounding_tools import DetectionClient

from dotenv import load_dotenv
load_dotenv()


logger = logging.getLogger(__name__)


class VideoAttributeExtractor:
    """Class for extracting attributes from video clips using various LLM backends.
    
    This class provides functionality to:
    1. Extract frames from video clips
    2. Process frames using different LLM backends (HuggingFace, OpenAI, ENNEW, ENNVL)
    3. Generate attributes and descriptions for video clips
    4. Handle audio extraction and processing
    """
    
    def __init__(self, tool_config: Dict[str, Any]) -> None:
        """Initialize the VideoAttributeExtractor with configuration settings.
        
        Args:
            tool_config: Configuration dictionary containing model and processing parameters
        """
        # LLM configuration
        self.llm_type = tool_config["llm_type"]
        self.model = tool_config.get('model', 'gpt-4o')
        self.temperature = tool_config.get('temperature', 0.5)
        self.top_k = tool_config.get('top_k', 0.5)
        self.top_p = tool_config.get('top_p', 0.5)
        self.num_beams = tool_config.get('num_beams', 0.5)
        self.max_tokens = tool_config.get('max_tokens', 4000)
        self.do_sample = tool_config.get('do_sample', True)
        
        # Video processing configuration
        self.sample_rate = tool_config.get('sample_rate', 1)
        self.sample_size = tool_config.get("sample_size", -1)
        self.sample_type = tool_config.get("sample_type", None)
        self.wav2vec_sample_rate = tool_config.get('wav2vec_sample_rate', 16000)
        self.max_try_count = tool_config.get('max_try_count', 5)
        self.short_side = tool_config.get('short_side', 512)
        
        # API configuration
        self.prompt = tool_config['prompt']
        self.gpt_api_key = os.getenv('GPT_API_KEY')
        self.base_url = os.getenv('GPT_BASE_URL')
        
        # Initialize components
        self._initialize_llm_client()
        self._initialize_tools(tool_config)
        
        # Audio processing state
        self.extract_audio = tool_config.get("extract_audio", False)
        self.audio_info = ""
        self.audio_info_path = None
        self.detect_info_path = None


    def load_prompt(self, prompt_path: str) -> Dict[str, Any]:
        """Load prompt from file."""
        with open(prompt_path, "r") as f:
            return yaml.load(f, Loader=yaml.FullLoader)

    def _initialize_tools(self, tool_config: Dict[str, Any]) -> None:
        """Initialize additional tools based on configuration.
        
        Args:
            tool_config: Configuration dictionary
        """
        self.grounding_tools = None
        if tool_config.get("detect_image", False):
            self.grounding_tools = DetectionClient()

    def _initialize_llm_client(self) -> None:
        """Initialize the appropriate LLM client based on configuration."""
        if self.llm_type == "huggingface":
            self._initialize_huggingface_client()
        elif self.llm_type == "openai":
            self.client = OpenAI(api_key=self.gpt_api_key, base_url=self.base_url)
        elif self.llm_type == "ENNEW":
            self.client = ChatEnnew(
                model=self.model,
                temperature=self.temperature,
                max_tokens=self.max_tokens,
                topP=self.top_p
            )
        elif self.llm_type == "ENNVL":
            self.client = ChatEnnvl(
                model=self.model,
                temperature=self.temperature,
                max_tokens=self.max_tokens,
                topP=self.top_p
            )
        else:
            raise ValueError(f"Invalid LLM type: {self.llm_type}")

    def _initialize_huggingface_client(self) -> None:
        """Initialize HuggingFace client with appropriate configuration."""
        try:
            from lmdeploy import pipeline, TurbomindEngineConfig, GenerationConfig
        except ImportError:
            raise ImportError("Please install the `lmdeploy` package to use the `internvl` LLM type.")
        
        world_size = torch.cuda.device_count()
        self.client = pipeline(
            self.model,
            backend_config=TurbomindEngineConfig(
                session_len=8192 ** 2,
                cache_max_entry_count=0.5,
                tp=world_size
            ),
            log_level='ERROR'
        )
        self.generation_config = GenerationConfig(
            max_new_tokens=self.max_tokens,
            do_sample=self.do_sample,
            temperature=self.temperature,
            top_k=self.top_k,
            top_p=self.top_p
        )

    # Image Processing Methods
    @staticmethod
    def short_side_resize(image: np.ndarray, short_side: int) -> Tuple[np.ndarray, float]:
        """Resize an image while maintaining its aspect ratio.
        
        Args:
            image: The input image
            short_side: Target length for the shortest side
            
        Returns:
            Tuple of (resized image, resize ratio)
        """
        h, w, _ = image.shape
        curr_short_side = min(h, w)
        resize_ratio = short_side / curr_short_side
        new_h = int(h * resize_ratio)
        new_w = int(w * resize_ratio)
        resized_image = cv2.resize(image, (new_w, new_h))
        return resized_image, resize_ratio
    
    def encode_cv2_image(self, image: np.ndarray) -> str:
        """Encode an image to a Base64 string.
        
        Args:
            image: The input image
            
        Returns:
            Base64-encoded string of the image
        """
        if self.grounding_tools is not None:
            try:
                image = self.grounding_tools.process_image_with_ai_analysis_and_boxes(
                    image, 
                    self.detect_info_path
                )
            except Exception as e:
                logger.error(f"Image processing failed: {str(e)}")
        
        _, buffer = cv2.imencode('.jpg', image)
        return base64.b64encode(buffer).decode('utf-8')

    # Video Processing Methods
    @staticmethod
    def read_frames(
        video_path: str,
        section: Dict[str, float],
        sample_rate: int,
        sample_type: Optional[str] = None,
        sample_size: int = -1
    ) -> Tuple[List[np.ndarray], List[int], Dict[str, Any]]:
        """Extract frames from a video.
        
        Args:
            video_path: Path to the video file
            section: Section of the video to extract frames from
            sample_rate: Sampling rate for frame extraction
            sample_type: Sampling method ("uniform", "random", or None)
            sample_size: Number of samples (if sample_type is not None)
            
        Returns:
            Tuple of (list of frames, sampled frame IDs, video info)
        """
        video_clip = VideoFileClip(video_path)
        fps = video_clip.fps
        frame_count = int(video_clip.duration * fps)
        width, height = video_clip.size

        video_info = {
            'width': width,
            'height': height,
            'frame_count': frame_count,
            'fps': fps
        }

        start_time = max(section['start_time'], 0) if section else 0
        end_time = min(section['end_time'], video_clip.duration) if section else video_clip.duration

        if sample_size == -1:
            sample_size = frame_count
        sample_size = min(sample_size, frame_count)
        
        sample_id_list = VideoAttributeExtractor._get_sample_ids(
            start_time, end_time, fps, sample_type, sample_size, frame_count
        )

        frame_list = [video_clip.get_frame(sample_id / fps) for sample_id in sample_id_list]
        video_clip.close()
        return frame_list, sample_id_list, video_info

    @staticmethod
    def _get_sample_ids(
        start_time: float,
        end_time: float,
        fps: float,
        sample_type: Optional[str],
        sample_size: int,
        frame_count: int
    ) -> List[int]:
        """Get list of frame IDs to sample based on sampling strategy.
        
        Args:
            start_time: Start time in seconds
            end_time: End time in seconds
            fps: Frames per second
            sample_type: Sampling method
            sample_size: Number of samples
            frame_count: Total frame count
            
        Returns:
            List of frame IDs to sample
        """
        if sample_type == "uniform":
            sample_interval = int(round(frame_count / sample_size))
            return list(range(
                int(start_time * fps),
                int(end_time * fps) - max(sample_interval, 1),
                max(sample_interval, 1)
            ))
        elif sample_type == "random":
            return random.sample(range(int(end_time * fps)), sample_size)
        else:
            sample_interval = int(round(fps / sample_rate))
            return list(range(
                int(start_time * fps),
                int(end_time * fps),
                max(sample_interval, 1)
            ))

    # LLM Response Methods
    def get_response_huggingface(
        self,
        prompt: str,
        frame_list: List[Image.Image],
        check_list: Optional[List[Any]] = None,
        json_type: bool = False,
        list_type: bool = False
    ) -> Union[Dict[str, Any], List[Any], str]:
        """Get response from Hugging Face LLM.
        
        Args:
            prompt: Input prompt
            frame_list: List of frames to process
            check_list: Optional list of valid responses
            json_type: Whether to parse response as JSON
            list_type: Whether to parse response as list
            
        Returns:
            Processed response
        """
        response = {}
        for _ in range(self.max_try_count):
            try:
                sess_response = self.client.chat(
                    (prompt, frame_list),
                    gen_config=self.generation_config
                )
                response_str = self._clean_response_string(sess_response.response.text)
                
                response = self._parse_response(response_str, json_type, list_type)
                
                if check_list is not None and response not in check_list:
                    continue
                break
            except Exception as e:
                logger.error(f"{response_str[:10]}...")
                logger.error(f"Exception during attribute extraction: {e}")
        return response

    def get_response_api(
        self,
        system_prompt: str,
        user_prompt: str,
        frame_list: List[str],
        llm_type: str,
        json_type: bool = False,
        list_type: bool = False,
        check_list: Optional[List[Any]] = None
    ) -> Union[Dict[str, Any], List[Any], str]:
        """Get response from API-based LLM.
        
        Args:
            system_prompt: System prompt
            user_prompt: User prompt
            frame_list: List of encoded frames
            llm_type: Type of LLM to use
            json_type: Whether to parse response as JSON
            list_type: Whether to parse response as list
            check_list: Optional list of valid responses
            
        Returns:
            Processed response
        """
        response = {}
        for _ in range(self.max_try_count):
            try:
                message = self._prepare_api_message(system_prompt, user_prompt, frame_list)
                response_str = self._get_api_response(message, llm_type)
                response_str = self._clean_response_string(response_str)
                
                response = self._parse_response(response_str, json_type, list_type)
                
                if check_list is not None and response not in check_list:
                    continue
                break
            except Exception as e:
                logger.error(f"Exception during attribute extraction: {e}")
        return response

    def _prepare_api_message(
        self,
        system_prompt: str,
        user_prompt: str,
        frame_list: List[str]
    ) -> List[Any]:
        """Prepare message for API request.
        
        Args:
            system_prompt: System prompt
            user_prompt: User prompt
            frame_list: List of encoded frames
            
        Returns:
            Formatted message list
        """
        return [
            SystemMessage(content=[{"type": "text", "text": system_prompt}]),
            HumanMessage(content=[
                {"type": "text", "text": user_prompt},
                *map(lambda x: {
                    "type": "image_url",
                    "image_url": {
                        "url": f'data:image/jpg;base64,{x}',
                        "detail": "high"
                    }
                }, frame_list)
            ])
        ]

    def _get_api_response(self, message: List[Any], llm_type: str) -> str:
        """Get response from API.
        
        Args:
            message: Prepared message
            llm_type: Type of LLM to use
            
        Returns:
            Response string
        """
        if llm_type in ['ENNEW', 'ENNVL']:
            result = self.client.invoke(message)
            return result.content
        elif llm_type == 'openai':
            result = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": message[0].content[0]["text"]},
                    {
                        "role": "user",
                        "content": [
                            message[1].content[0]["text"],
                            *map(lambda x: {"image": x}, message[1].content[1:]),
                        ],
                    },
                ],
                max_tokens=self.max_tokens,
                temperature=self.temperature
            )
            return result.choices[0].message.content
        else:
            raise ValueError("Invalid LLM type. Choose either 'ENNEW', 'ENNVL', or 'openai'.")

    @staticmethod
    def _clean_response_string(response_str: str) -> str:
        """Clean response string for parsing.
        
        Args:
            response_str: Raw response string
            
        Returns:
            Cleaned response string
        """
        response_str = re.sub(r'(?<!")([a-zA-Z0-9_]+)(?!"):', r'"\1":', response_str)
        response_str = re.sub(r'\\n', '', response_str)
        response_str = re.sub(r'\\[tr]', '', response_str)
        response_str = re.sub(r',\s*}', '}', response_str)
        response_str = re.sub(r',\s*\]', ']', response_str)
        return response_str

    @staticmethod
    def _parse_response(
        response_str: str,
        json_type: bool,
        list_type: bool
    ) -> Union[Dict[str, Any], List[Any], str]:
        """Parse response string based on type.
        
        Args:
            response_str: Cleaned response string
            json_type: Whether to parse as JSON
            list_type: Whether to parse as list
            
        Returns:
            Parsed response
        """
        if json_type:
            return json.loads(response_str)
        elif list_type:
            return eval(response_str)
        return response_str

    # Attribute Extraction Methods
    def get_attribute_per_clip_huggingface(
        self,
        frame_list: List[np.ndarray],
        video_clip_info: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Extract attributes from a clip using Hugging Face LLM.
        
        Args:
            frame_list: List of frames to process
            video_clip_info: Dictionary containing clip information
            
        Returns:
            Updated clip information with extracted attributes
        """
        process_frames = [Image.fromarray(frame).convert('RGB') for frame in frame_list]
        video_prefix = ''.join([f'Frame{i+1}: <image>\n' for i in range(len(process_frames))])
        prompt_template = self.load_prompt(self.prompt["prompt"])
        system_prompt = prompt_template["system"] + video_prefix
        prompt_dict = {}
        
        return self._process_prompt_template(
            prompt_template,
            system_prompt,
            process_frames,
            prompt_dict,
            video_clip_info,
            "huggingface"
        )

    def get_attribute_per_clip_api(
        self,
        frame_list: List[np.ndarray],
        video_clip_info: Dict[str, Any],
        llm_type: str
    ) -> Dict[str, Any]:
        """Extract attributes from a clip using API-based LLM.
        
        Args:
            frame_list: List of frames to process
            video_clip_info: Dictionary containing clip information
            llm_type: Type of LLM to use
            
        Returns:
            Updated clip information with extracted attributes
        """
        frame_list = [self.short_side_resize(frame, self.short_side)[0] for frame in frame_list]
        encoded_frame_list = [self.encode_cv2_image(frame) for frame in frame_list]
        
        video_prefix = ''.join([f'Frame{i+1}: <image>\n' for i in range(len(encoded_frame_list))])
        prompt_template = self.load_prompt(self.prompt["prompt"])
        system_prompt = prompt_template["system"] + video_prefix
        prompt_dict = {}
        
        return self._process_prompt_template(
            prompt_template,
            system_prompt,
            encoded_frame_list,
            prompt_dict,
            video_clip_info,
            llm_type
        )

    def _process_prompt_template(
        self,
        prompt_template: Dict[str, Any],
        system_prompt: str,
        frame_list: List[Any],
        prompt_dict: Dict[str, Any],
        video_clip_info: Dict[str, Any],
        llm_type: str
    ) -> Dict[str, Any]:
        """Process prompt template and update video clip info.
        
        Args:
            prompt_template: Template for prompts
            system_prompt: System prompt
            frame_list: List of frames
            prompt_dict: Dictionary to store prompt variables
            video_clip_info: Video clip information
            llm_type: Type of LLM to use
            
        Returns:
            Updated video clip information
        """
        user_prompts = prompt_template["users"]
        next_id = "begin"
        
        while True:
            user_input = user_prompts[next_id]
            if user_input["type"] == "end":
                break
            
            next_id = self._handle_prompt_step(
                user_input,
                system_prompt,
                frame_list,
                prompt_dict,
                llm_type
            )
        
        for output_variable in prompt_template["final_output_variables"]:
            video_clip_info[output_variable] = prompt_dict[output_variable]
        
        return video_clip_info

    def _handle_prompt_step(
        self,
        user_input: Dict[str, Any],
        system_prompt: str,
        frame_list: List[Any],
        prompt_dict: Dict[str, Any],
        llm_type: str
    ) -> str:
        """Handle a single prompt step.
        
        Args:
            user_input: Current prompt step
            system_prompt: System prompt
            frame_list: List of frames
            prompt_dict: Dictionary to store prompt variables
            llm_type: Type of LLM to use
            
        Returns:
            Next prompt step ID
        """
        if user_input["type"] == "variable":
            for name, val in zip(user_input["names"], user_input["values"]):
                prompt_dict[name] = val
            return user_input["next"]
            
        elif user_input["type"] == "set_variables":
            for name in user_input["names"]:
                prompt_dict[name] = getattr(self, name)
            return user_input["next"]
            
        elif user_input["type"] == "prompt":
            input_variables = user_input["input_variables"]
            main_text = user_input["main_text"]
            if len(input_variables) != 0:
                main_text = main_text.format(*[prompt_dict[var] for var in input_variables])
            
            output_args = user_input.get("output_args", {})
            response = self._get_llm_response(
                system_prompt,
                main_text,
                frame_list,
                llm_type,
                **output_args
            )
            
            output_variables = user_input["output_variables"]
            if len(output_variables) == 1:
                prompt_dict[output_variables[0]] = response
            else:
                for var, r in zip(output_variables, response):
                    prompt_dict[var] = r
            return user_input["next"]
            
        elif user_input["type"] == "gate":
            input_variables = user_input["input_variables"]
            expression = user_input["expression"].format(
                *[f"prompt_dict['{var}']" for var in input_variables]
            )
            gate_result = eval(expression)
            return user_input["true_next"] if gate_result else user_input["false_next"]

    def _get_llm_response(
        self,
        system_prompt: str,
        main_text: str,
        frame_list: List[Any],
        llm_type: str,
        **kwargs
    ) -> Any:
        """Get response from LLM.
        
        Args:
            system_prompt: System prompt
            main_text: Main prompt text
            frame_list: List of frames
            llm_type: Type of LLM to use
            **kwargs: Additional arguments for response processing
            
        Returns:
            Processed response
        """
        for _ in range(self.max_try_count):
            try:
                if llm_type == "huggingface":
                    response = self.get_response_huggingface(
                        prompt=main_text,
                        frame_list=frame_list,
                        **kwargs
                    )
                else:
                    response = self.get_response_api(
                        system_prompt=system_prompt,
                        user_prompt=main_text,
                        frame_list=frame_list,
                        llm_type=llm_type,
                        **kwargs
                    )
                if response == "r":
                    continue
                break
            except Exception as e:
                logger.error(f"Exception during attribute extraction: {e}")
        return response

    # Main Processing Method
    def run(
        self,
        video_path: str,
        save_path: Optional[str] = None
    ) -> Dict[str, Any]:
        """Extract attributes from a video file.
        
        Args:
            video_path: Path to the video file
            save_path: Optional path to save results
            
        Returns:
            Dictionary containing extracted attributes
        """
        if save_path is not None:
            self.audio_info_path = os.path.join(
                save_path.replace(".json", "_audio_info.json")
            )
            self.detect_info_path = os.path.join(
                save_path.replace(".json", "_detect_info.json")
            )

        # Extract video info
        video_clip = VideoFileClip(video_path)
        video_info = {
            'video_path': video_path,
            'width': video_clip.size[0],
            'height': video_clip.size[1],
            'frame_count': int(video_clip.duration * video_clip.fps),
            'fps': video_clip.fps,
            'duration': video_clip.duration,
            'start_time': 0,
            'end_time': video_clip.duration,
            'start_frame': 0,
            'end_frame': int(video_clip.duration * video_clip.fps)
        }
        video_clip.close()
        
        # Extract audio if configured
        if self.extract_audio:
            try:
                audio_info = video_to_text(video_path, self.audio_info_path)
            except Exception as e:
                audio_info = ""
            self.audio_info = audio_info
        
        # Extract frames
        frame_list, sample_id_list, _ = self.read_frames(
            video_path,
            {
                'start_time': video_info['start_time'],
                'end_time': video_info['end_time'],
                'start_frame': video_info['start_frame'],
                'end_frame': video_info['end_frame']
            },
            self.sample_rate,
            self.sample_type,
            self.sample_size
        )
        
        # Process frames based on LLM type
        if self.llm_type == "huggingface":
            video_info = self.get_attribute_per_clip_huggingface(
                frame_list,
                video_info
            )
        elif self.llm_type in ["openai", "ENNEW", "ENNVL"]:
            video_info = self.get_attribute_per_clip_api(
                frame_list,
                video_info,
                self.llm_type
            )
        else:
            raise ValueError(f"Invalid LLM type: {self.llm_type}")
        
        # Save results if path provided
        if save_path is not None:
            with open(save_path, 'w') as f:
                json.dump(video_info, f, indent=2, ensure_ascii=False)
        
        return video_info