import os
import re
import base64
import cv2
import tempfile
import requests
import numpy as np
import json
from pathlib import Path
from typing import List, Dict, Any, Optional
import logging

from dds_cloudapi_sdk import Config, Client
from dds_cloudapi_sdk.tasks.v2_task import V2Task
from langchain_core.messages import HumanMessage
from dotenv import load_dotenv

from ..LLM.ENNVL import ChatEnnvl

# Load environment variables
load_dotenv()

# Configure logging
logger = logging.getLogger(__name__)

# Constants
PROMPT = """Analyze the image and identify all main objects. Follow these rules:
1. List ALL main objects, separated by periods
2. Use simple, clear object names
3. Do not include any additional text or explanations
4. Format: object1.object2.object3.object4...

Example outputs:
- person.car.tree.building
- dog.ball.cat.tree.bird
- chair.table.lamp.picture.plant"""


class DetectionClient:
    """Client for object detection and image analysis using DINO and MLLM models."""
    
    def __init__(
        self,
        detect_model: str = "DINO-X-1.0",
        mllm_model: str = "gpt-4o",
        temperature: float = 0,
        max_tokens: int = 4000,
        topP: float = 1.0,
        bbox_threshold: float = 0.25,
        iou_threshold: float = 0.8,
        timeout: int = 5
    ) -> None:
        """
        Initialize the DetectionClient.
        
        Args:
            detect_model: DINO model version to use
            mllm_model: MLLM model to use for analysis
            temperature: Temperature for MLLM generation
            max_tokens: Maximum tokens for MLLM generation
            topP: Top-p sampling parameter for MLLM
            bbox_threshold: Confidence threshold for bounding boxes
            iou_threshold: IOU threshold for non-maximum suppression
            timeout: Request timeout in seconds
        """
        # Initialize DINO client
        self.config = Config(os.getenv('GROUNDING_TOKEN'))
        self.detect_client = Client(self.config)
        self.detect_model = detect_model
        self.bbox_threshold = bbox_threshold
        self.iou_threshold = iou_threshold
        self.timeout = timeout
        
        # Initialize MLLM client
        self.mllm_model = mllm_model
        self.mllm_client = ChatEnnvl(
            model=self.mllm_model,
            temperature=temperature,
            max_tokens=max_tokens,
            topP=topP
        )
        
        # Create temporary directory
        self.temp_dir = tempfile.mkdtemp(prefix='grounding_')
    
    def __del__(self) -> None:
        """Clean up temporary files when object is destroyed."""
        if os.path.exists(self.temp_dir):
            for file in Path(self.temp_dir).glob('*'):
                try:
                    file.unlink()
                except Exception as e:
                    logger.error(f"Error deleting temporary file {file}: {e}")
            try:
                os.rmdir(self.temp_dir)
            except Exception as e:
                logger.error(f"Error deleting temporary directory {self.temp_dir}: {e}")

    def save_temp_image(self, image: np.ndarray, prefix: str = 'temp') -> str:
        """
        Save image to temporary file.
        
        Args:
            image: Image to save
            prefix: Prefix for the temporary file name
            
        Returns:
            Path to the saved temporary file
        """
        temp_path = os.path.join(
            self.temp_dir,
            f"{prefix}_{next(tempfile._get_candidate_names())}.jpg"
        )
        cv2.imwrite(temp_path, image)
        return temp_path

    def _validate_and_fix_format(self, response_str: str) -> str:
        """
        Validate and fix the response format to ensure it matches the expected pattern.
        
        Args:
            response_str: Raw response string from MLLM
            
        Returns:
            Cleaned and validated response string
        """
        # Clean the response
        cleaned = self._clean_response(response_str)
        
        # Process objects
        objects = [obj.strip().lower() for obj in cleaned.split('.') if obj.strip()]
        objects = [re.sub(r'[^a-z0-9]', '', obj) for obj in objects]
        
        # Remove duplicates while preserving order
        seen = set()
        objects = [obj for obj in objects if obj and obj not in seen and not seen.add(obj)]
        
        # Return default if no valid objects
        if not objects:
            return "object."
        
        return '.'.join(objects) + '.'

    def analyze_image_with_gpt4v(self, image: np.ndarray) -> str:
        """
        Analyze image using GPT-4V to identify main objects.
        
        Args:
            image: Image to analyze
            
        Returns:
            List of identified objects in the required format
        """
        # Save and encode image
        temp_path = self.save_temp_image(image, 'analysis')
        with open(temp_path, 'rb') as f:
            base64_image = base64.b64encode(f.read()).decode('utf-8')
        
        # Create message for MLLM
        message = [
            HumanMessage(content=[
                {"type": "text", "text": PROMPT},
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f'data:image/jpg;base64,{base64_image}',
                        "detail": "high"
                    }
                }
            ])
        ]
        
        # Get and validate response
        result = self.mllm_client.invoke(message)
        validated_response = self._validate_and_fix_format(result.content)
        
        # Log for debugging
        logger.debug(f"Original response: {result.content}")
        logger.debug(f"Validated response: {validated_response}")
        
        return validated_response

    def _clean_response(self, response_str: str) -> str:
        """
        Clean the response string to match the required format.
        
        Args:
            response_str: Raw response string
            
        Returns:
            Cleaned response string
        """
        # Remove unwanted characters
        cleaned = re.sub(r'[\n\r\t]', '', response_str)
        cleaned = re.sub(r'^[^a-zA-Z0-9]*', '', cleaned)
        cleaned = re.sub(r'[^a-zA-Z0-9.]*$', '', cleaned)
        cleaned = re.sub(r'\.+', '.', cleaned)
        cleaned = re.sub(r'\s+', '', cleaned)
        
        # Ensure proper ending
        return cleaned.rstrip('.') + '.'

    def process_image(
        self,
        image: np.ndarray,
        prompt_text: str = "main object",
        targets: List[str] = ["bbox"]
    ) -> Dict[str, Any]:
        """
        Process image using DINO model for object detection.
        
        Args:
            image: Image to process
            prompt_text: Text prompt for detection
            targets: List of detection targets
            
        Returns:
            Detection results
        """
        temp_path = self.save_temp_image(image, 'process')
        image_url = self.detect_client.upload_file(temp_path)
        task = self.create_task(image_url, prompt_text, targets)
        return self.run_task(task)

    def process_image_with_ai_analysis_and_boxes(
        self,
        image: np.ndarray,
        detect_info_path: Optional[str] = None,
        targets: List[str] = ["bbox"]
    ) -> np.ndarray:
        """
        Process image with AI analysis and draw bounding boxes.
        
        Args:
            image: Image to process
            detect_info_path: Optional path to save/load detection info
            targets: List of detection targets
            
        Returns:
            Image with bounding boxes drawn
        """
        # Load existing results if available
        if detect_info_path and os.path.exists(detect_info_path):
            logger.debug(f"[Skipping] Loading detect info from {detect_info_path}")
            with open(detect_info_path, 'r') as f:
                result = json.load(f)
        else:
            # Generate new analysis
            prompt_text = self.analyze_image_with_gpt4v(image)
            logger.debug(f"AI generated label: {prompt_text}")
            result = self.process_image(image, prompt_text, targets)
            
            # Save results if path provided
            if detect_info_path:
                with open(detect_info_path, 'w') as f:
                    json.dump(result, f)
        
        return self.get_image_with_boxes(image, result)

    def get_image_with_boxes(
        self,
        image: np.ndarray,
        result: Dict[str, Any]
    ) -> np.ndarray:
        """
        Draw bounding boxes on the image based on detection results.
        
        Args:
            image: Original image
            result: Detection results
            
        Returns:
            Image with bounding boxes drawn
        """
        if image is None:
            raise ValueError("Unable to read image")
            
        for box in result.get('objects', []):
            logger.debug(box)
            x1, y1, x2, y2 = map(int, box['bbox'])
            cv2.rectangle(image, (x1, y1), (x2, y2), (0, 0, 255), 2)
        
        return image

    def create_task(
        self,
        image_url: str,
        prompt_text: str = "main object",
        targets: List[str] = ["bbox"]
    ) -> V2Task:
        """
        Create a DINO detection task.
        
        Args:
            image_url: URL of the image to process
            prompt_text: Text prompt for detection
            targets: List of detection targets
            
        Returns:
            Configured V2Task object
        """
        task = V2Task(
            api_path="/v2/task/dinox/detection",
            api_body={
                "model": self.detect_model,
                "image": image_url,
                "prompt": {"type": "text", "text": prompt_text},
                "targets": targets,
                "bbox_threshold": self.bbox_threshold,
                "iou_threshold": self.iou_threshold
            }
        )
        task.set_request_timeout(self.timeout)
        return task

    def run_task(self, task: V2Task) -> Dict[str, Any]:
        """
        Run a DINO detection task.
        
        Args:
            task: Task to run
            
        Returns:
            Task results
        """
        self.detect_client.run_task(task)
        return task.result


if __name__ == "__main__":
    # Example usage
    client = DetectionClient()
    infer_image_url = "https://dds-frontend.oss-accelerate.aliyuncs.com/static_files/playground/grounding_DINO-1.6/02.jpg"

    # Download and process image
    with requests.get(infer_image_url) as response:
        image = cv2.imdecode(
            np.frombuffer(response.content, np.uint8),
            cv2.IMREAD_COLOR
        )

    # Process image and save result
    image_with_boxes = client.process_image_with_ai_analysis_and_boxes(image)
    cv2.imwrite("result_with_boxes.jpg", image_with_boxes)
    print("Saved image with bounding boxes to result_with_boxes.jpg")