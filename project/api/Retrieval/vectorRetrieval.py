import os
from dotenv import load_dotenv

from langchain_community.vectorstores.milvus import Milvus, DEFAULT_MILVUS_CONNECTION
from langchain_core.documents import Document
from typing import List
from typing_extensions import TypedDict, Optional, Any, Tuple
from langchain_openai import OpenAIEmbeddings
from ..LLM.ENNEW import ChatEnnew
from langchain_core.messages import SystemMessage, HumanMessage

import json
import openai
import logging
from pymilvus import WeightedRanker, AnnSearchRequest

load_dotenv()

logger = logging.getLogger(__name__)

MAX_LENGTH = 65_535

class State(TypedDict):
    question: str
    context: List[Document]
    answer: str

class VectorRetrieval(Milvus):
    def __init__(self, config: dict):
        """Initialize the Milvus vector store."""
        try:
            from pymilvus import Collection, utility
        except ImportError:
            raise ImportError(
                "Please install it with `pip install pymilvus`."
                "Could not import pymilvus python package. "
            )
        
        self.default_search_params = {
            "IVF_FLAT": {"metric_type": "L2", "params": {"nprobe": 10}},
            "IVF_SQ8": {"metric_type": "L2", "params": {"nprobe": 10}},
            "IVF_PQ": {"metric_type": "L2", "params": {"nprobe": 10}},
            "HNSW": {"metric_type": "L2", "params": {"ef": 10}},
            "RHNSW_FLAT": {"metric_type": "L2", "params": {"ef": 10}},
            "RHNSW_SQ": {"metric_type": "L2", "params": {"ef": 10}},
            "RHNSW_PQ": {"metric_type": "L2", "params": {"ef": 10}},
            "IVF_HNSW": {"metric_type": "L2", "params": {"nprobe": 10, "ef": 10}},
            "ANNOY": {"metric_type": "L2", "params": {"search_k": 10}},
            "SCANN": {"metric_type": "L2", "params": {"search_k": 10}},
            "AUTOINDEX": {"metric_type": "L2", "params": {}},
            "GPU_CAGRA": {
                "metric_type": "L2",
                "params": {
                    "itopk_size": 128,
                    "search_width": 4,
                    "min_iterations": 0,
                    "max_iterations": 0,
                    "team_size": 0,
                },
            },
            "GPU_IVF_FLAT": {"metric_type": "L2", "params": {"nprobe": 10}},
            "GPU_IVF_PQ": {"metric_type": "L2", "params": {"nprobe": 10}},
        }
        
        dense_embedding_type = config.get("dense_embedding_type", "api")
        
        if dense_embedding_type == "api":
            self.chunk_size = config.get("chunk_size", 500)
            self.dense_embedding_func_url = os.getenv("EMBEDDING_URL")
            self.dense_embedding_func_api_key = os.getenv("EMBEDDING_API_KEY")
            self.dense_embedding_func_model = os.getenv("EMBEDDING_MODEL")
            self.check_embedding_ctx_length = config.get("check_embedding_ctx_length", False)
            self.dense_embedding_func = OpenAIEmbeddings(base_url=self.dense_embedding_func_url, 
                                                         api_key=self.dense_embedding_func_api_key, 
                                                         model=self.dense_embedding_func_model,
                                                         check_embedding_ctx_length=self.check_embedding_ctx_length,
                                                         chunk_size=self.chunk_size)
        elif dense_embedding_type == "huggingface":
            try:
                from langchain_huggingface import HuggingFaceEmbeddings
            except ImportError:
                raise ImportError(
                    "Please install it with `pip install langchain_huggingface`."
                    "Could not import langchain_huggingface python package. "
                )
            # Bug: embedding_func_model -> dense_embedding_func_model
            self.dense_embedding_func_model = os.getenv("EMBEDDING_MODEL")
            model_kwargs = {'trust_remote_code': True}
            self.dense_embedding_func = HuggingFaceEmbeddings(model_name=self.dense_embedding_func_model,
                                                            model_kwargs=model_kwargs)
        self.connection_args_url = os.getenv("DATASET_CONNECTION_URL", None)
        self.connection_args_token = os.getenv("DATASET_CONNECTION_TOKEN", None)
        self.connection_args_address = os.getenv("DATASET_CONNECTION_ADDRESS", None)

        connection_args = dict(
            uri=self.connection_args_url,
            token=self.connection_args_token,
            address=self.connection_args_address
        )
        
        self.collection_name: str = config.get("collection_name")
        self.collection_description: str = config.get("collection_description", "")
        self.collection_properties: Optional[dict[str, Any]] = config.get("collection_properties", None)
        self.index_params: Optional[dict] = config.get("index_params", None)
        self.search_params: Optional[dict] = config.get("search_params", None)
        self.consistency_level = config.get("consistency_level", "Session")
        self.auto_id: bool = config.get("auto_id", True)
        drop_old: Optional[bool] = config.get("drop_old", False)
        
        self.pre_limit = config.get("pre_limit", 10)
        self.limit = config.get("limit", 10)
        self.weight_list = config.get("weight_list")
        
        self.partition_names = config.get("partition_names", None)
        self._partition_key_field = config.get("partition_key_field", None)
        self.replica_number = config.get("replica_number", 1)
        self.timeout = config.get("timeout", None)
        self.num_shards = config.get("num_shards", None)
        
        # Create the connection to the server
        if connection_args is None:
            connection_args = DEFAULT_MILVUS_CONNECTION
        self.alias = self._create_connection_alias(connection_args)
        self.col: Optional[Collection] = None
        
        # Grab the existing collection if it exists
        if utility.has_collection(self.collection_name, using=self.alias):
            self.col = Collection(
                self.collection_name,
                using=self.alias,
            )
            if self.collection_properties is not None:
                self.col.set_properties(self.collection_properties)
        
        # If need to drop old, drop it
        if drop_old and isinstance(self.col, Collection):
            self.col.drop()
            self.col = None
        
        self.fields_config: dict = config.get("fields", dict())
        self.fields: dict = dict()
        self.enable_dynamic_field: bool = config.get("enable_dynamic_field", True)
        self._primary_field: str = config.get("primary_field", "pk")
        self._vector_fields = []
        self._index_fields = config.get("index_fields", [])
        self._meta_fields = config.get("meta_fields", [])
        self._return_fields = config.get("return_fields", self._meta_fields)
        
        self.poi_upper_bound = config.get("poi_upper_bound", 0.6)
        self.poi_lower_bound = config.get("poi_lower_bound", 0.5)
        
        self.use_pre_process = config.get("use_pre_process", False)
        if self.use_pre_process:
            self.max_try_count = config.get("max_try_count", 3)
            self.pre_process_cfg = config.get("pre_process_cfg", dict())
            self.pre_process_client = ChatEnnew(
                model=self.pre_process_cfg.get("model", "gpt-4o"),
                temperature=self.pre_process_cfg.get("temperature", 0.0),
                topP=self.pre_process_cfg.get("top_p", 1.0),
                max_tokens=self.pre_process_cfg.get("max_tokens", 1000)
            )
            self.pre_process_prompt = self.pre_process_cfg.get("pre_process_prompt", dict())
        
        # Initialize the vector store
        self._extract_fields()
        self._create_index()
        self._create_search_params()
        self._load(
            partition_names=self.partition_names,
            replica_number=self.replica_number,
            timeout=self.timeout,
        )
            
    def _create_search_params(self) -> None:
        """Generate search params based on the current index type"""
        from pymilvus import Collection

        if isinstance(self.col, Collection) and self.search_params is None:
            indexes = self._get_index()
            if indexes is not None:
                self.search_params = dict()
                for index in indexes:
                    index_type: str = index["index_param"]["index_type"]
                    metric_type: str = index["index_param"]["metric_type"]
                    search_param = self.default_search_params[index_type]
                    search_param["metric_type"] = metric_type
                    self.search_params[index["field"]] = search_param
    
    def _get_index(self) -> Optional[dict[str, Any]]:
        """Return the vector index information if it exists"""
        from pymilvus import Collection

        if isinstance(self.col, Collection):
            indexes = []
            for x in self.col.indexes:
                if x.field_name in self._index_fields:
                    indexes.append(x.to_dict())
            return None if len(indexes) == 0 else indexes
        return None
    
    def _extract_fields(self) -> None:
        """Grab the existing fields from the Collection"""
        from pymilvus import Collection, DataType

        if isinstance(self.col, Collection):
            schema = self.col.schema
            for x in schema.fields:
                field_params = x.to_dict()
                if field_params["type"] is DataType.FLOAT_VECTOR:
                    self._vector_fields.append(x.name)
                self.fields[x.name] = field_params
    
    def _create_index(self) -> None:
        """Create a index on the collection"""
        from pymilvus import Collection, MilvusException

        if isinstance(self.col, Collection) and self._get_index() is None:
            try:
                # If no index params, use a default HNSW based one
                if self.index_params is None:            
                    self.index_params = {
                        "metric_type": "L2",
                        "index_type": "HNSW",
                        "params": {"M": 8, "efConstruction": 64},
                    }

                for field_name in self._index_fields:                    
                    try:
                        self.col.create_index(
                            field_name,
                            index_params=self.index_params,
                            using=self.alias,
                            index_name=f"{field_name}_index"
                        )

                    # If default did not work, most likely on Zilliz Cloud
                    except MilvusException:
                        # Use AUTOINDEX based index
                        self.index_params = {
                            "metric_type": "L2",
                            "index_type": "AUTOINDEX",
                            "params": {},
                        }
                        self.col.create_index(
                            field_name,
                            index_params=self.index_params,
                            using=self.alias,
                            index_name=f"f{field_name}_index"
                        )
                    logger.debug(
                        "Successfully created an index on collection: %s",
                        self.collection_name,
                    )

            except MilvusException as e:
                logger.error(
                    "Failed to create an index on collection: %s", self.collection_name
                )
                raise e
    
    def _create_collection(self, field_datas: dict):
        from pymilvus import (
            Collection,
            CollectionSchema,
            DataType,
            FieldSchema,
            MilvusException,
        )
        from pymilvus.orm.types import numpy_dtype_str_map
        
        numpy_dtype_str_map["dense_vector"] = DataType.FLOAT_VECTOR
        numpy_dtype_str_map["sparse_vector"] = DataType.SPARSE_FLOAT_VECTOR
        field_schemas = []
        for field_name, field_params in self.fields_config.items():
            field_dtype = numpy_dtype_str_map[field_params["dtype"]]
            field_kwargs = dict(name = field_name)
            
            if field_dtype == DataType.FLOAT_VECTOR:
                str_kwargs = field_kwargs.pop("str_kwargs", {})
                str_kwargs["max_length"] = str_kwargs.get("max_length", MAX_LENGTH)
                field_schema_str = FieldSchema(
                    field_name,
                    DataType.VARCHAR,
                    **str_kwargs
                )
                field_schemas.append(field_schema_str)
                
                vector_name = f"{field_name}_vector"
                dim = len(field_datas[vector_name][0])
                field_kwargs.update(field_params)
                field_kwargs["name"] = vector_name
                field_kwargs["dtype"] = field_dtype
                field_kwargs["dim"] = dim
                self._vector_fields.append(vector_name)
            else:
                field_kwargs.update(field_params)
                field_kwargs["dtype"] = field_dtype
            field_schema = FieldSchema(**field_kwargs)
            field_schemas.append(field_schema)

        # Create the primary key field
        if self.auto_id:
            field_schemas.append(
                FieldSchema(
                    self._primary_field, 
                    DataType.INT64, 
                    is_primary=True, 
                    auto_id=True
                )
            )
        else:
            field_schemas.append(
                FieldSchema(
                    self._primary_field,
                    DataType.VARCHAR,
                    is_primary=True,
                    auto_id=False,
                    max_length=MAX_LENGTH,
                )
            )
        
        for schema in field_schemas:
            logger.debug("schema:", schema)
                
        # Create the schema for the collection
        schema = CollectionSchema(
            field_schemas,
            description=self.collection_description,
            partition_key_field=self._partition_key_field,
            enable_dynamic_field=self.enable_dynamic_field
        )

        # Create the collection
        try:
            if self.num_shards is not None:
                # Issue with defaults:
                # https://github.com/milvus-io/pymilvus/blob/59bf5e811ad56e20946559317fed855330758d9c/pymilvus/client/prepare.py#L82-L85
                self.col = Collection(
                    name=self.collection_name,
                    schema=schema,
                    consistency_level=self.consistency_level,
                    using=self.alias,
                    num_shards=self.num_shards,
                )
            else:
                self.col = Collection(
                    name=self.collection_name,
                    schema=schema,
                    consistency_level=self.consistency_level,
                    using=self.alias,
                )
            # Set the collection properties if they exist
            if self.collection_properties is not None:
                self.col.set_properties(self.collection_properties)
        except MilvusException as e:
            logger.error(
                "Failed to create collection: %s error: %s", self.collection_name, e
            )
            raise e
    
    def _load(
        self,
        partition_names: Optional[list] = None,
        replica_number: int = 1,
        timeout: Optional[float] = None,
    ) -> None:
        """Load the collection if available."""
        from pymilvus import Collection, utility
        from pymilvus.client.types import LoadState

        timeout = self.timeout or timeout
        if (
            isinstance(self.col, Collection)
            and self._get_index() is not None
            and utility.load_state(self.collection_name, using=self.alias)
            == LoadState.NotLoad
        ):
            self.col.load(
                partition_names=partition_names,
                replica_number=replica_number,
                timeout=timeout,
            )
    
    def _add_records(self, 
                    records: List[dict],
                    timeout: Optional[float] = None,
                    batch_size: int = 1000,
                    *,
                    ids: Optional[List[str]] = None,
                    **kwargs: Any,
                ) -> List[str]:
        from pymilvus import Collection, MilvusException, DataType
        
        if not self.auto_id:
            assert isinstance(
                ids, list
            ), "A list of valid ids are required when auto_id is False."
            assert len(set(ids)) == len(
                records
            ), "Different lengths of texts and unique ids are provided."
            assert all(
                len(x.encode()) <= 65_535 for x in ids
            ), "Each id should be a string less than 65535 bytes."

        
        if len(records) == 0:
            logger.debug("Nothing to insert, skipping.")
            return []
        
        field_datas = dict()
        for field_name, field_params in self.fields_config.items():
            # Debug for _add_records
            # field_data = [record[field_name] for record in records] 
            
            # 检查字段是否存在于记录中，先给如"attribute_caption_useless", "highlight" 等填空字段
            field_data = []
            for record in records:
                if field_name in record:
                    field_data.append(record[field_name])  # 正常填充
                else:
                    # 根据字段类型填充默认值
                    field_dtype = field_params["dtype"]
                    if field_dtype == "str":
                        field_data.append("")  # 填充空字符串
                    elif field_dtype == "float":
                        field_data.append(0.0)  # 填充 0.0
                    else:
                        field_data.append(None)  # 其他类型填充 None

            # 如果字段缺失，打印缺失的字段名称
            missing_count = sum(1 for record in records if field_name not in record)
            if missing_count > 0:
                logger.warning(f"Field '{field_name}' is missing in {missing_count} records.")
                    
            
            if isinstance(field_data[0], list):
                field_data = [";".join(text) for text in field_data]
            field_datas[field_name] = field_data
            field_dtype = field_params["dtype"]
            if field_dtype in ["dense_vector"]:
                try:
                    embedding = self.dense_embedding_func.embed_documents(field_data)
                except NotImplementedError:
                    embedding = [self.dense_embedding_func.embed_query(x) for x in field_data]
                except openai._exceptions.BadRequestError:
                    embedding = [self.dense_embedding_func.embed_query(x) for x in field_data]
                                    
                field_datas[f"{field_name}_vector"] = embedding

        # If the collection hasn't been initialized yet, perform all steps to do so
        if not isinstance(self.col, Collection):
            self._create_collection(field_datas=field_datas)
            self._extract_fields()
            self._create_index()
            self._create_search_params()
            self._load(
                partition_names=self.partition_names,
                replica_number=self.replica_number,
                timeout=self.timeout,
            )
        
        if not self.auto_id:
            field_datas[self._primary_field] = ids  # type: ignore[assignment]

        # Total insert count
        total_count = len(records)

        pks: list[str] = []
                
        assert isinstance(self.col, Collection)
        for i in range(0, total_count, batch_size):
            # Grab end index
            end = min(i + batch_size, total_count)
            # Convert dict to list of lists batch for insertion
            insert_list = [
                field_datas[x.name][i:end] for x in self.col.schema.fields if x.name in field_datas
            ]
                        
            # Insert into the collection.
            try:
                timeout = self.timeout or timeout
                res: Collection = self.col.insert(insert_list, timeout=timeout, **kwargs)
                pks.extend(res.primary_keys)
            except MilvusException as e:
                logger.error(
                    "Failed to insert batch starting at entity: %s/%s", i, total_count
                )
                raise e
        return pks
    
    def _get_expr(self, record: dict):
        from pymilvus import DataType
        expr = ""
        for field_name in self._meta_fields:
            value = f"{record[field_name]}"
            dtype = self.fields_config[field_name]["dtype"]
            if dtype == "str":
                value = f'"{value}"'
            expr += f'{field_name} == {value} and '
        expr = expr[:-5]
        return expr
    
    def _query_contexts_by_ids(self, ids: List[int]):
        expr = f'pk in {ids}'
        datas = self.col.query(expr, output_fields=["text"])
        contexts = [x.get("text") for x in datas]
        return contexts
    
    def _get_contexts(self, record: dict):
        expr = self._get_expr(record)
        ids = super().get_pks(expr)
        contexts = self._query_contexts_by_ids(ids)
        return contexts
    
    def upsert_records(self, records: List[dict]):        
        '''如已经存在相同视频片段数据，直接覆盖，否则添加'''
        if records is None or len(records) == 0:
            logger.debug("No Records to upsert.")
            return None
        records_to_upsert = []
        ids_to_upsert = []
        records_to_add = []
        for record in records:
            expr = self._get_expr(record)
            id_list = super().get_pks(expr)
            if id_list is None or len(id_list) == 0:
                records_to_add.append(record)
            else:
                records_to_upsert.append(record)
                ids_to_upsert.append(id_list[0])
        if len(ids_to_upsert):    
            self.delete(ids=ids_to_upsert)
        self._add_records(records=records)

    def only_add_records(self, records: List[dict]):
        '''如已经存在相同视频片段数据，则不添加，否则添加'''
        if records is None or len(records) == 0:
            logger.debug("No Records to upsert.")
            return None
        records_to_add = []
        for record in records:
            expr = self._get_expr(record)
            id_list = super().get_pks(expr)
            if id_list is None or len(id_list) == 0:
                records_to_add.append(record)
        self._add_records(records=records_to_add)

    def delete_records(self, records: List[dict]):
        '''删除指定视频片段数据'''
        for record in records:
            expr = self._get_expr(record)
            super().delete(expr)
    
    def update_records(self, records, create_type="upsert"):
        if create_type == "upsert":
            self.upsert_records(records)
        elif create_type == "add":
            self.only_add_records(records)
        else:
            raise ValueError("type must be one of upsert and only_add")
    
    
    def switch_to_retrieval_list(self, vector_retrieval_result: list, query: str):
        retrieval_list = []

        for i in range(len(vector_retrieval_result)):
            vector_info_dict = vector_retrieval_result[i][0]
            retrieval_score = vector_retrieval_result[i][1]
            retrieval_clip_dict = {k: v for k, v in vector_info_dict.items() if k != self._primary_field}
            retrieval_clip_dict['retrieval_score'] = retrieval_score
            retrieval_list.append(retrieval_clip_dict)
        
        return {query:retrieval_list}

    @staticmethod
    def switch_multilist_to_list_and_set(retrieval_video_info_list):
        video_info_list_str = list(set([json.dumps(item) for d in retrieval_video_info_list for value in d.values() for item in value]))
        video_info_list = [json.loads(item) for item in video_info_list_str]
        return video_info_list

    @staticmethod
    def merge_and_deduplicate_videos(data):
        """
        将多个字典中的 video_retrieval_list 拼接成一个列表并去重。

        Args:
            data_list (list): 包含多个字典的数据列表，每个字典可能包含 video_retrieval_list 键。

        Returns:
            list: 拼接并去重后的 video_retrieval_list。
        """
        # all_video_retrievals = []

        # # 遍历数据中的每个字典
        # for item in data:
        #     if "video_retrieval_list" in item:
        #         # 将 video_retrieval_list 中的内容添加到集合中
        #         all_video_retrievals.extend(item["video_retrieval_list"])

        # # 使用 json.dumps 将字典转换为字符串进行去重
        # unique_video_retrievals = [json.loads(item) for item in {json.dumps(d, sort_keys=True) for d in all_video_retrievals}]

        return data
    
    def run(self, 
            query: str,
            params: Optional[List[dict]] = None,
            timeout: Optional[float] = None,
            **kwargs: Any
            ) -> List[Tuple[Document, float]]:
        
        if self.use_pre_process:
            for _ in range(self.max_try_count):
                try:
                    message = [
                        SystemMessage(content=[{"type": "text", "text": self.pre_process_prompt["system"]}]),
                        HumanMessage(content=[{"type": "text", "text": self.pre_process_prompt["user"].format(query=query)}])
                    ]
                    query = self.pre_process_client.invoke(message).content
                    print(f"query: {query}")
                    break
                except Exception as e:
                    print(f"Error getting ennew-langchain retrieval: {e}")
            
        
        if self.col is None:
            logger.debug("No existing collection to search.")
            return []
        
        if self.weight_list is None:
            self.weight_list = {field: 1.0 for field in self.search_params}
        
        self.weight_list = {field: weight / sum(self.weight_list.values()) for field, weight in self.weight_list.items()}
        
        self.col.flush()
        vector_count = self.col.num_entities
        limit = min(self.limit, vector_count)
        
        if params is None:
            params = self.search_params
        query_vector = self.dense_embedding_func.embed_query(query)        
                
        # 优先进行POI检索
        results_poi = self.col.search(
            data = [query_vector],
            anns_field = "poi_vector",
            param = params["poi_vector"],
            limit = limit,
            output_fields = self._return_fields,
            timeout = timeout,
            **kwargs
        )
        
        
        absolute_poi_ret = []
        relative_poi_ret = []
        
        for result in results_poi[0]:
            data = {x: result.entity.get(x) for x in self._return_fields}
            
            score = 1 - result.score
            pair = (data, result.score)
            
            if score >= self.poi_upper_bound:            
                absolute_poi_ret.append(pair)
            elif score >= self.poi_lower_bound:
                relative_poi_ret.append(pair)
        
        
        if len(absolute_poi_ret) > 0:
            return absolute_poi_ret
        
        
        requests = []
        weights =[]
        
        if len(relative_poi_ret) > 0:
            self.weight_list["poi_vector"] = 1.0
            self.weight_list = {field: weight / sum(self.weight_list.values()) for field, weight in self.weight_list.items()}
        
        for field_name, weight in self.weight_list.items():
            search_kwargs = dict(
                data = [query_vector],
                anns_field = field_name,
                param = params[field_name],
                limit = self.pre_limit
            )
            request = AnnSearchRequest(**search_kwargs)
            requests.append(request)
            weights.append(weight)
                
        rerank= WeightedRanker(*weights) 
        
        timeout = self.timeout or timeout
        res = self.col.hybrid_search(requests,
                                     rerank=rerank,
                                     limit=limit,
                                     output_fields=self._return_fields,
                                     timeout=timeout,
                                     **kwargs)
        # Organize results.
        ret = []
        for result in res[0]:
            data = {x: result.entity.get(x) for x in self._return_fields}
            pair = (data, result.score)
            ret.append(pair)
        return ret
    
