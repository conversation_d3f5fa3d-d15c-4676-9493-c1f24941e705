import math
import re
import ast
import os
import json
from typing import List, Dict, Any, <PERSON><PERSON>
from concurrent.futures import <PERSON>hr<PERSON><PERSON>oolExecutor

from openai import OpenAI
from pydantic import BaseModel, Field
from langchain_core.messages import HumanMessage, SystemMessage
from dotenv import load_dotenv
import yaml

from ..LLM.ENNEW import ChatEnnew

# Load environment variables
load_dotenv()


class RetrievalStructureOutput(BaseModel):
    """Model for structured retrieval output."""
    retrieval: List[int] = Field(
        description='Your task is to locate related <clip id> according to the given instructions and return a list of <clip id>. For example: [2,5,6]'
    )


def auto_parse_clip_result(result_str: str) -> List[Tuple[int, float]]:
    """
    Parse and clean the clip result string into a list of tuples.
    
    Args:
        result_str: Raw result string from LLM
        
    Returns:
        List of tuples containing (index, score)
    """
    # Clean the string
    cleaned = re.sub(r"```(?:json)?", "", result_str, flags=re.IGNORECASE).strip("` \n")
    cleaned = cleaned.replace("\n", '').replace("'", '"').replace(""", '"').replace(""", '"').replace(" ", "")
    cleaned = "\n".join(line.strip() for line in cleaned.splitlines() if line.strip())
    
    # Try parsing as JSON first
    try:
        parsed = json.loads(cleaned)
        assert isinstance(parsed, list)
        return parsed
    except json.JSONDecodeError:
        pass
    
    # Try parsing with ast as fallback
    try:
        return ast.literal_eval(cleaned)
    except Exception as e:
        print(f"❌ Failed to parse list format: {e}")
        return []


class LLMRetrieval:
    """Class for handling LLM-based video clip retrieval."""
    
    def __init__(self, tool_config: Dict[str, Any]):
        """
        Initialize the LLMRetrieval class.
        
        Args:
            tool_config: Configuration dictionary containing model and call parameters
        """
        # Initialize API credentials
        self.gpt_api_key = os.getenv('GPT_API_KEY')
        self.base_url = os.getenv('GPT_BASE_URL')
        
        # Initialize OpenAI client
        try:
            self.client = OpenAI(api_key=self.gpt_api_key, base_url=self.base_url)
        except Exception as e:
            self.client = None
            print(f"[Warning] OpenAI load error. Use your own gpt api key or ENNEW: {e}")
        
        # Load configuration
        self.tool_config = tool_config
        self.model = tool_config.get('model', 'gpt-4o')
        self.invalid_keyword_list = ["sorry", "cannot assist", "cannot provide", "can't assist"]
        self.short_side = tool_config.get('short_side', 512)
        self.max_try_count = tool_config.get('max_try_count', 5)
        self.empty_caption = tool_config['empty_caption']
        self.system_retrieval_prompt = self.load_prompt(tool_config['retrieval_prompt'])["prompt"]["system"]
        self.sample_rate = tool_config['sample_rate']
        self.max_retrieval_clip_num = tool_config.get('max_retrieval_clip_num', 20)
        self.retrieval_keys = tool_config.get('retrieval_keys', ['raw_caption'])
        
        # Model parameters
        self.topk = tool_config.get('topk', 5)
        self.max_tokens = tool_config['max_tokens']
        self.temperature = tool_config.get('temperature', 0.5)
        self.top_p = tool_config.get('top_p', 0.9)
        
        # Format prompts
        self._format_prompts()
        
        # Initialize ENNEW
        self.ennew_langchain = ChatEnnew(
            model=self.model,
            temperature=self.temperature,
            topP=self.top_p,
            max_tokens=self.max_tokens
        )

    def load_prompt(self, prompt_path: str) -> Dict[str, str]:
        """Load prompts from a YAML file."""
        with open(prompt_path, 'r') as file:
            return yaml.safe_load(file)

    def _format_prompts(self) -> None:
        """Format system and user prompts with configuration values."""
        self.system_retrieval_prompt = self.system_retrieval_prompt.replace("[TOPK]", str(self.topk))
        tag_str = ", ".join(f"<{tag}>" for tag in self.retrieval_keys)
        self.system_retrieval_prompt = self.system_retrieval_prompt.replace("[TAG]", tag_str)
        
        self.user_retrieval_prompt = self.load_prompt(self.tool_config['retrieval_prompt'])["prompt"]["user"]
        self.user_retrieval_prompt = self.user_retrieval_prompt.replace("[TAG]", tag_str)

    def get_ennew_langchain_retrieval(self, user_prompt: str, system_prompt: str) -> str:
        """
        Get retrieval results from ENNEW langchain.
        
        Args:
            user_prompt: User prompt for retrieval
            system_prompt: System prompt for retrieval
            
        Returns:
            Retrieval result string
        """
        for _ in range(self.max_try_count):
            try:
                message = [
                    SystemMessage(content=[{"type": "text", "text": system_prompt}]),
                    HumanMessage(content=[{"type": "text", "text": user_prompt}])
                ]
                return self.ennew_langchain.invoke(message).content
            except Exception as e:
                print(f"Error getting ennew-langchain retrieval: {e}")
        return ""

    @staticmethod
    def split_list_by_length(lst: List[Any], avg_len: int, n: float = 1/5, m: int = 3) -> List[List[Any]]:
        """
        Split a list into chunks of approximately equal length.
        
        Args:
            lst: List to split
            avg_len: Target average length of chunks
            n: Minimum ratio for remainder
            m: Minimum size for remainder
            
        Returns:
            List of list chunks
        """
        num_chunks = math.ceil(len(lst) / avg_len)
        remainder = len(lst) % avg_len
        
        # Adjust number of chunks if remainder is too small
        if remainder < max(avg_len * n, m) and remainder:
            num_chunks -= 1
        
        # Split into chunks
        chunks = []
        start = 0
        for _ in range(num_chunks):
            end = start + avg_len
            chunks.append(lst[start:end])
            start = end
        
        # Handle remaining elements
        if start < len(lst):
            if chunks:
                chunks[-1].extend(lst[start:])
            else:
                chunks.append(lst[start:])
        
        return chunks

    @staticmethod
    def format_by_keys(video_info_list: List[Dict[str, Any]], keys: List[str]) -> str:
        """
        Format video information into a string representation.
        
        Args:
            video_info_list: List of video information dictionaries
            keys: List of keys to include in formatting
            
        Returns:
            Formatted string of video information
        """
        formatted_info = []
        for id, info in enumerate(video_info_list):
            cap = [f'<{info[k]}>' for k in keys]
            formatted_info.append(f'<{id}>: {", ".join(cap)}'.strip().replace("\n", ""))
        return "|\n".join(formatted_info)

    def run(self, video_info_list: Dict[str, Any], retrieval_instruction: str) -> Dict[str, List[Dict[str, Any]]]:
        """
        Retrieve video clips based on descriptions and instructions.
        
        Args:
            video_info_list: Dictionary containing video information
            retrieval_instruction: Instructions for retrieval
            
        Returns:
            Dictionary containing retrieved clips
        """
        slices = self.split_list_by_length(video_info_list, self.max_retrieval_clip_num)
        retrieval_id_list = []
        retrieved_slices = {}

        def process_slice(slice_id: int) -> Tuple[int, List[Tuple[int, float]]]:
            """Process a single slice of video information."""
            video_info_str = self.format_by_keys(slices[slice_id], self.retrieval_keys)
            
            system_prompt = self.system_retrieval_prompt.replace("[INS]", retrieval_instruction)
            user_prompt = self.user_retrieval_prompt.replace("[INS]", retrieval_instruction)
            user_prompt = user_prompt.replace("[CLIP_DATA]", video_info_str)
            
            raw_result = self.get_ennew_langchain_retrieval(user_prompt, system_prompt)
            return slice_id, auto_parse_clip_result(raw_result)

        # Process slices in parallel
        with ThreadPoolExecutor() as executor:
            results = list(executor.map(process_slice, range(len(slices))))

        # Collect results
        for slice_id, retrieval_id_slice_list in results:
            retrieved_slices[slice_id] = retrieval_id_slice_list

        # Adjust indices and collect results
        for slice_id, id_slice_list in sorted(retrieved_slices.items()):
            slice_offset = slice_id * len(id_slice_list)
            adjusted_ids = [(int(index) + slice_offset, score) for index, score in id_slice_list]
            retrieval_id_list.extend(adjusted_ids)

        # Process final results
        retrieval_slices_list = []
        for index, score in retrieval_id_list:
            video_info_list[index]["llm_first_score"] = score
            retrieval_slices_list.append(video_info_list[index])
        
        # Final retrieval step
        video_info_str = self.format_by_keys(
            retrieval_slices_list, 
            self.retrieval_keys + ["llm_first_score"]
        )
        
        system_prompt = self.system_retrieval_prompt.replace("[INS]", retrieval_instruction)
        user_prompt = self.user_retrieval_prompt.replace("[INS]", retrieval_instruction)
        user_prompt = user_prompt.replace("[CLIP_DATA]", video_info_str)
        
        raw_retrieval_result_sum = self.get_ennew_langchain_retrieval(user_prompt, system_prompt)
        retrieval_id_result_sum = auto_parse_clip_result(raw_retrieval_result_sum)
        
        if not retrieval_id_result_sum:
            retrieval_id_result_sum = [(index, 1) for index in range(self.topk)]
        
        # Prepare final output
        retrieval_dict = {retrieval_instruction: []}
        for index, score in retrieval_id_result_sum:
            retrieval_slices_list[int(index)]["llm_second_score"] = score
            retrieval_dict[retrieval_instruction].append(retrieval_slices_list[int(index)])
        
        return retrieval_dict