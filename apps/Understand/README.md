# Gradio 使用
## Gradio 界面使用
在/SmartVideoClip_submit 路径下，运行：
~~~
python apps/Understand/gradio_app_understand.py 
~~~

在本地端口 http://127.0.0.1:7861，打开Gradio页面，将想要理解的视频拖拽即可。

示例如下:
![alt text](../../assert/image_gradio.png)

## Gradio 视频理解模型使用说明
可以通过代码gradio_app_understand.py 文件中的，config_und 配置，更改视频理解模型的配置。
~~~
def get_understanding_config():
    config = {
            "config_und": 'model/Understand/understand.yaml', 
    }
    return config
~~~
默认使用'model/Understand/understand.yaml'最稳定的理解模型。

## Gradio 保存设置
默认保存的配置如下：
1. 上传的视频保存在'./save/tmp_videos' 下，以4f84d09a-edac-469c-9b4a-52bcf7cdb8ef 为例，保存视频为4f84d09a-edac-469c-9b4a-52bcf7cdb8ef.mp4。

2. 大模型检测json文件：通过大模型对视频的关键帧进行检测，检测出其主要示例的分类(category) 和 对应的位置(box),辅助模型进行理解。保存的检测json文件为4f84d09a-edac-469c-9b4a-52bcf7cdb8ef_und_detect_info.json。

3. 视频理解json文件：通过大模型对视频的关键帧进行粗细粒度结合的理解，分为场景类别，场景先验信息，主题，粗粒度标签，细粒度标签等。json 样例如下：
~~~json
{
  "video_path": "save/tmp_videos/4f84d09a-edac-469c-9b4a-52bcf7cdb8ef.mp4",
  "width": 1080,
  "height": 1920,
  "frame_count": 413,
  "fps": 30.0,
  "duration": 13.77,
  "start_time": 0,
  "end_time": 13.77,
  "start_frame": 0,
  "end_frame": 413,
  "scene_type": "海边栈道",
  "scene_info": "- 海鸥飞翔  \n- 栈道上有行人  \n- 天气晴朗  \n- 栈道靠近海域  \n- 水面波光粼粼  \n- 人物穿着休闲",
  "raw_caption": "晴朗天气下，海鸥在栈道上空飞翔，人物休闲地沿着栈道行走，手扔食物吸引海鸥俯冲捕捉，海水波光粼粼近在脚边。",
  "coarse_attributes": "[\"海鸥\", \"大量\", \"飞翔俯冲\", \"海边栈道\", \"白天\", \"人物互动\", \"轻松\"]",
  "fine_attributes": "[\"晴朗天气\", \"湛蓝天空\", \"海鸥飞翔俯冲\", \"栈道行人\", \"人物轻松行走\", \"手抛食物\", \"海鸥俯冲捕捉\", \"栈道护栏金属质\", \"绿色海水波光\", \"轻松愉悦氛围\"]"
}
~~~

# FastAPI 使用
在/SmartVideoClip_submit 路径下，运行：
~~~
uvicorn apps.Understand.understand_api:app --reload
~~~
FastAPI的服务端口为http://127.0.0.1:8000，访问此端口，发送请求即可进行视频理解。

请求示例，如'tests/test_understand_api.py'，运行：
~~~
python tests/test_understand_api.py
~~~

## 服务器端log 输出
服务器端log输出示例如下，表示正确使用视频理解模型，视频成功下载，视频理解模型正确输出。
~~~log
INFO:     Started server process [2848442]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
File downloaded and saved as: ./tmp/5ce75e20-3c1d-4fcb-9c20-671cbfb12bb0.MP4
Successfully downloaded video to: ./tmp/5ce75e20-3c1d-4fcb-9c20-671cbfb12bb0.MP4
Successfully generated attributes: {'video_path': './tmp/5ce75e20-3c1d-4fcb-9c20-671cbfb12bb0.MP4', 'width': 1920, 'height': 1080, 'frame_count': 279, 'fps': 29.97002997002997, 'duration': 9.31, 'start_time': 0, 'end_time': 9.31, 'start_frame': 0, 'end_frame': 279, 'scene_type': '海边岩石观景台', 'scene_info': 'r', 'raw_caption': '海岸植被茂盛，风力强劲，草丛与灌木形成密集景观，木制观景台竖立于前景，背景映衬开阔海面与云层密布的天空。', 'coarse_attributes': '["岩石观景台", "一处", "无明显行为", "海边观景台", "傍晚", "无互动", "宁静"]', 'fine_attributes': '["茂盛的海岸植被", "岩石观景台结构", "沿海强风环境", "展示宁静景色", "傍晚柔和光线", "木制观景台材质", "灌木丛密集布局", "蓝天与云层盖顶", "远眺开阔海面", "自然景观观景点", "层次感植物景观"]'}
INFO:     127.0.0.1:60704 - "POST /video/understanding?video_url=https%3A%2F%2Ftx.enn.cn%2Fgroup1%2FM00%2F0B%2FE9%2FCiaAUmdg61mAflSHAgLgxNwEGIk888.MP4 HTTP/1.1" 200 OK
~~~

## 客户端log 输出
客户端log 输出示例如下，表示成功接受服务器端的返回值，视频理解模型成功调用。
~~~json
API调用成功！
返回结果： {
    "result": {
        "video_path": "./tmp/5ce75e20-3c1d-4fcb-9c20-671cbfb12bb0.MP4",
        "width": 1920,
        "height": 1080,
        "frame_count": 279,
        "fps": 29.97002997002997,
        "duration": 9.31,
        "start_time": 0,
        "end_time": 9.31,
        "start_frame": 0,
        "end_frame": 279,
        "scene_type": "海边岩石观景台",
        "scene_info": "r",
        "raw_caption": "海岸植被茂盛，风力强劲，草丛与灌木形成密集景观，木制观景台竖立于前景，背景映衬开阔海面与云层密布的天空。",
        "coarse_attributes": "[\"岩石观景台\", \"一处\", \"无明显行为\", \"海边观景台\", \"傍晚\", \"无互动\", \"宁静\"]",
        "fine_attributes": "[\"茂盛的海岸植被\", \"岩石观景台结构\", \"沿海强风环境\", \"展示宁静景色\", \"傍晚柔和光线\", \"木制观景台材质\", \"灌木丛密集布局\", \"蓝天与云层盖顶\", \"远眺开阔海面\", \"自然景观观景点\", \"层次感植物景观\"]"
    }
}
~~~






