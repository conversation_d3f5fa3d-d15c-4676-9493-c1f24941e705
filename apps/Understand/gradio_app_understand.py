import os
import sys
import gradio as gr
import yaml

# Add project root to system path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "../.."))
sys.path.append(project_root)

from project import VideoAttributeExtractor

# Load configuration
CONFIG_PATH = 'model/Understand/understand.yaml'

with open(CONFIG_PATH, "r") as yaml_file:
    config_und_params = yaml.safe_load(yaml_file)

# Initialize video attribute extractor
attribute_gen = VideoAttributeExtractor(config_und_params)


def process_video(video_file):
    """
    Process the uploaded video and return the understanding results.
    
    Args:
        video_file: Path to the uploaded video file
        
    Returns:
        str: Formatted string containing video understanding results
    """
    result = attribute_gen.run(video_file)
    if not result:
        return "无法处理视频，请重试。"
        
    show_result = "视频理解结果：\n"
    meta_keys = {
        "raw_caption": "描述信息",
        "coarse_attributes": "粗粒度标签：",
        "fine_attributes": "细粒度标签：",
    }
    
    for key, value in meta_keys.items():
        if key in result:
            tmp = result[key].replace("r\"\"\"", "").replace("r", "")
            show_result += f"{value}: {tmp}\n"
        
    return show_result


def create_interface():
    """Create and return the Gradio interface."""
    with gr.Blocks(title="课题十：视频理解系统") as demo:
        gr.Markdown("## 课题十：视频理解系统\n上传视频并点击 **开始理解** 按钮以获取视频的理解结果。")
        
        with gr.Row():
            video_input = gr.Video(
                label="上传视频",
                interactive=True,
                max_length=100,
                height=500,
                width=400
            )
            output_info = gr.Textbox(
                label="理解结果",
                lines=20,
                interactive=False
            )
        
        with gr.Row():
            process_btn = gr.Button("开始理解", variant="primary")
        
        process_btn.click(
            fn=process_video,
            inputs=[video_input],
            outputs=[output_info]
        )
    
    return demo


if __name__ == "__main__":
    demo = create_interface()
    demo.launch(
        debug=True,
        share=False,
        inbrowser=True,
        server_port=7861
    )