# Standard library imports
import os
import uuid
from typing import Any
from urllib.parse import urlparse

# Third-party imports
import yaml
import requests
from fastapi import FastAP<PERSON>, Query
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder
from fastapi.middleware.cors import CORSMiddleware

# Local imports
from project import VideoAttributeExtractor

# Initialize FastAPI app
app = FastAPI()

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

def download_file(url: str, save_dir: str = "./tmp", use_raw: bool = False) -> tuple:
    """
    Download a file from a given URL.
    
    Args:
        url (str): URL of the file to download
        save_dir (str): Directory to save the file
        use_raw (bool): Whether to use raw filename or generate UUID
        
    Returns:
        tuple: (file_path, file_name) or (None, None) if download fails
    """
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)

    parsed_url = urlparse(url)
    file_name = os.path.basename(parsed_url.path)
    
    if not use_raw:
        file_extension = os.path.splitext(file_name)[1]
        unique_file_name = f"{str(uuid.uuid4())}{file_extension}"
        file_path = os.path.join(save_dir, unique_file_name)
    else:
        file_path = save_dir

    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()

        with open(file_path, "wb") as file:
            for chunk in response.iter_content(chunk_size=8192):
                file.write(chunk)

        print(f"File downloaded and saved as: {file_path}")
        return file_path, file_name

    except requests.exceptions.RequestException as e:
        print(f"Download failed: {e}")
        return None, None

def download_videos(video_url: str, save_dir: str = "./tmp") -> str:
    """
    Download video from the provided URL.
    
    Args:
        video_url (str): URL of the video to download
        save_dir (str): Directory to save the video
        
    Returns:
        str: Path to the downloaded video file
    """
    video_path, _ = download_file(video_url, save_dir=save_dir, use_raw=False)
    if video_path is None:
        raise ValueError("Failed to download video")
    return video_path

def get_understanding_config() -> dict:
    """Get configuration for understanding module."""
    return {
        "config_und": 'model/Understand/understand.yaml'
    }

# Load configuration
config = get_understanding_config()
with open(config["config_und"], "r") as yaml_file:
    config_und_params = yaml.safe_load(yaml_file)

# Initialize attribute generator
attribute_gen = VideoAttributeExtractor(config_und_params)

def create_response(data: Any, message: str = "", code: int = 0) -> JSONResponse:
    """Create a standardized JSON response."""
    return JSONResponse(content={"code": code, "data": data, "message": message})

@app.post("/video/understanding")
async def understanding(video_url: str = Query(..., description="URL of the video to process")):
    """
    Endpoint for video understanding detection.
    
    Args:
        video_url (str): URL of the video to process
        
    Returns:
        JSONResponse: Understanding detection results
    """
    try:
        # Download videos
        video_path = download_videos(video_url)
        print(f"Successfully downloaded video to: {video_path}")
        
        # Generate attributes
        result = attribute_gen.run(video_path)
        print(f"Successfully generated attributes: {result}")
        
        # Serialize results
        understanding_results_serialized = jsonable_encoder(result)
        
        return create_response(
            data={
                "result": understanding_results_serialized,
            },
            message="Understanding detection completed successfully",
            code=0
        )
        
    except Exception as e:
        return create_response(
            data=None,
            message=f"Error during detection: {str(e)}",
            code=1
        )