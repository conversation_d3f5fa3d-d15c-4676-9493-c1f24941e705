"""FastAPI application for video retrieval service."""

import yaml
from typing import Any, Dict, Optional
from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.responses import JSONResponse

from pydantic import BaseModel
from project import VectorRetrieval


class QueryInput(BaseModel):
    """Input model for video retrieval query."""
    
    query: str
    user_limit: Optional[int] = 5


class RetrievalConfig:
    """Configuration manager for retrieval service."""
    
    def __init__(self) -> None:
        self.config_path = 'model/Retrieval/vectorRetrieval.yaml'
        self.retrieval_params = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """Load retrieval configuration from YAML file."""
        with open(self.config_path, "r") as yaml_file:
            return yaml.safe_load(yaml_file)


def create_response(data: Any, message: str = "", code: int = 0) -> JSONResponse:
    """Create standardized JSON response.
    
    Args:
        data: Response data
        message: Response message
        code: Response status code
        
    Returns:
        JSONResponse: Formatted response
    """
    return JSONResponse(content={"code": code, "data": data, "message": message})


# Initialize FastAPI app
app = FastAPI(
    title="Video Retrieval API",
    description="API for semantic video retrieval",
    version="1.0.0"
)

# Initialize configuration and retrieval system
config_manager = RetrievalConfig()
vector_retriever = VectorRetrieval(config_manager.retrieval_params)


@app.post("/video/retrieval")
async def retrieve_videos(input_data: QueryInput) -> JSONResponse:
    """Retrieve videos based on semantic query.
    
    Args:
        input_data: QueryInput containing search query and optional limit
        
    Returns:
        JSONResponse: Retrieval results or error message
        
    Raises:
        HTTPException: If retrieval fails
    """
    try:
        # Execute similarity search
        result = vector_retriever.run(
            query=input_data.query,
            user_limit=input_data.user_limit
        )
        
        # Convert results to retrieval list format
        retrieval_result = vector_retriever.switch_to_retrieval_list(
            result,
            input_data.query
        )
        
        return create_response(
            data=retrieval_result,
            message="Retrieval completed successfully",
            code=0
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error during retrieval: {str(e)}"
        )