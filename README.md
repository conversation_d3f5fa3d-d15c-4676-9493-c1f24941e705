# 项目说明
课题十的核心是对输入复杂场景的视频进行多粒度理解和对数据库中的视频进行准确检索。发现现有的视频理解模型无法理解特定的场景，因此本研究提出了新的理解算法和视频检索算法。

<center>
    <img src="assert/project.png" alt="alt text" width="500" height="auto">
</center>

## 视频理解算法
视频理解算法:研究通过设计高效的prompt来实现对复杂视频场景的准确理解
1. 对Video 的场景进行简单分类。
2. 使用大语言模型生成对应场景的先验知识，辅助视觉理解。
3. 由整体到局部的视频理解策略，首先理解视频的主要内容，再从主体、事件、环境、时间情感角度生成粗粒度标签，最后再对标签进行细化。
4. 结合音频信息，从多个模态实现对视频的理解。

## 视频检索算法
视频检索算法:研究设计向量检索和大语吉模型精检检索的复合检索方式。
1. 多向量检索加权:对视频的主要内容、粗粒度标签、细粒度标签赋不同权重，通过多个角度结合的方式辅助视频检索。
2. 大语吉模型精检检索:对通过向量检索的视频进行细粒度的检索，通过大语言模型对视频的标签进行语义级别的理解，从而检索出更精准的结果。
3. 视觉检测器提示:对用户的输入的query，使用提示词进行转化，使其更富有语义层面的信息，辅助视频检索算法。

## 仓库说明
本课题提交所有测试代码、Gradio可视化代码、API调用代码，包含视频理解算法、视频检索算法调用等文档记录。


# 快速开始：APPS 使用说明
## Gradio 使用
### Gradio 视频理解模型界面使用
在/SmartVideoClip_submit 路径下，运行：
~~~
python apps/Understand/gradio_app_understand.py 
~~~

在本地端口 http://127.0.0.1:7861，打开Gradio页面，将想要理解的视频拖拽即可。

示例如下:
<center>
    <img src="assert/image_gradio.png" alt="alt text" width="800" height="auto">
</center>

### Gradio 视频理解模型使用说明
可以通过代码gradio_app_understand.py 文件中的，config_und 配置，更改视频理解模型的配置。
~~~python
def get_understanding_config():
    config = {
            "config_und": 'model/Understand/understand.yaml', 
    }
    return config
~~~
默认使用'model/Understand/understand.yaml'最稳定的理解模型。

### Gradio 视频理解模型保存设置
默认保存的配置如下：
1. 上传的视频保存在'./save/tmp_videos' 下，以4f84d09a-edac-469c-9b4a-52bcf7cdb8ef 为例，保存视频为4f84d09a-edac-469c-9b4a-52bcf7cdb8ef.mp4。

2. 大模型检测json文件：通过大模型对视频的关键帧进行检测，检测出其主要示例的分类(category) 和 对应的位置(box),辅助模型进行理解。保存的检测json文件为4f84d09a-edac-469c-9b4a-52bcf7cdb8ef_und_detect_info.json。

3. 视频理解json文件：通过大模型对视频的关键帧进行粗细粒度结合的理解，分为场景类别，场景先验信息，主题，粗粒度标签，细粒度标签等。json 样例如下：
~~~json
{
  "video_path": "save/tmp_videos/4f84d09a-edac-469c-9b4a-52bcf7cdb8ef.mp4",
  "width": 1080,
  "height": 1920,
  "frame_count": 413,
  "fps": 30.0,
  "duration": 13.77,
  "start_time": 0,
  "end_time": 13.77,
  "start_frame": 0,
  "end_frame": 413,
  "scene_type": "海边栈道",
  "scene_info": "- 海鸥飞翔  \n- 栈道上有行人  \n- 天气晴朗  \n- 栈道靠近海域  \n- 水面波光粼粼  \n- 人物穿着休闲",
  "raw_caption": "晴朗天气下，海鸥在栈道上空飞翔，人物休闲地沿着栈道行走，手扔食物吸引海鸥俯冲捕捉，海水波光粼粼近在脚边。",
  "coarse_attributes": "[\"海鸥\", \"大量\", \"飞翔俯冲\", \"海边栈道\", \"白天\", \"人物互动\", \"轻松\"]",
  "fine_attributes": "[\"晴朗天气\", \"湛蓝天空\", \"海鸥飞翔俯冲\", \"栈道行人\", \"人物轻松行走\", \"手抛食物\", \"海鸥俯冲捕捉\", \"栈道护栏金属质\", \"绿色海水波光\", \"轻松愉悦氛围\"]"
}
~~~

## FastAPI 使用
### 视频理解模型FastAPI使用
在/SmartVideoClip_submit 路径下，运行：
~~~
uvicorn apps.Understand.understand_api:app --reload
~~~
FastAPI的服务端口为http://127.0.0.1:8000，访问此端口，发送请求即可进行视频理解。

请求示例，如'tests/test_understand_api.py'，运行：
~~~
pytest -s -v tests/test_understand_api.py
~~~
#### 服务器端log 输出
服务器端log输出示例如下，表示正确使用视频理解模型，视频成功下载，视频理解模型正确输出。
~~~log
INFO:     Started server process [2848442]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
File downloaded and saved as: ./tmp/5ce75e20-3c1d-4fcb-9c20-671cbfb12bb0.MP4
Successfully downloaded video to: ./tmp/5ce75e20-3c1d-4fcb-9c20-671cbfb12bb0.MP4
Successfully generated attributes: {'video_path': './tmp/5ce75e20-3c1d-4fcb-9c20-671cbfb12bb0.MP4', 'width': 1920, 'height': 1080, 'frame_count': 279, 'fps': 29.97002997002997, 'duration': 9.31, 'start_time': 0, 'end_time': 9.31, 'start_frame': 0, 'end_frame': 279, 'scene_type': '海边岩石观景台', 'scene_info': 'r', 'raw_caption': '海岸植被茂盛，风力强劲，草丛与灌木形成密集景观，木制观景台竖立于前景，背景映衬开阔海面与云层密布的天空。', 'coarse_attributes': '["岩石观景台", "一处", "无明显行为", "海边观景台", "傍晚", "无互动", "宁静"]', 'fine_attributes': '["茂盛的海岸植被", "岩石观景台结构", "沿海强风环境", "展示宁静景色", "傍晚柔和光线", "木制观景台材质", "灌木丛密集布局", "蓝天与云层盖顶", "远眺开阔海面", "自然景观观景点", "层次感植物景观"]'}
INFO:     127.0.0.1:60704 - "POST /video/understanding?video_url=https%3A%2F%2Ftx.enn.cn%2Fgroup1%2FM00%2F0B%2FE9%2FCiaAUmdg61mAflSHAgLgxNwEGIk888.MP4 HTTP/1.1" 200 OK
~~~

#### 客户端log 输出
客户端log 输出示例如下，表示成功接受服务器端的返回值，视频理解模型成功调用。
~~~json
API调用成功！
返回结果： {
    "result": {
        "video_path": "./tmp/5ce75e20-3c1d-4fcb-9c20-671cbfb12bb0.MP4",
        "width": 1920,
        "height": 1080,
        "frame_count": 279,
        "fps": 29.97002997002997,
        "duration": 9.31,
        "start_time": 0,
        "end_time": 9.31,
        "start_frame": 0,
        "end_frame": 279,
        "scene_type": "海边岩石观景台",
        "scene_info": "r",
        "raw_caption": "海岸植被茂盛，风力强劲，草丛与灌木形成密集景观，木制观景台竖立于前景，背景映衬开阔海面与云层密布的天空。",
        "coarse_attributes": "[\"岩石观景台\", \"一处\", \"无明显行为\", \"海边观景台\", \"傍晚\", \"无互动\", \"宁静\"]",
        "fine_attributes": "[\"茂盛的海岸植被\", \"岩石观景台结构\", \"沿海强风环境\", \"展示宁静景色\", \"傍晚柔和光线\", \"木制观景台材质\", \"灌木丛密集布局\", \"蓝天与云层盖顶\", \"远眺开阔海面\", \"自然景观观景点\", \"层次感植物景观\"]"
    }
}
~~~

### 检索算法FastAPI使用
#### 检索算法服务器端
在/SmartVideoClip_submit 路径下，运行：
~~~
uvicorn apps.Retrieval.retrieval_api:app --reload
~~~
开启服务，默认的FastAPI端口为http://127.0.0.1:8000 ，可自行更改。

#### 检索算法客户端
在/SmartVideoClip_submit 路径下，运行：
~~~
pytest -s -v tests/test_retrieval_api.py
# -s: 输出print的内容
# -v: verbose 模式，显示每个测试函数名
~~~
可以的到如下的检索输出示例：PASS表示通过单元测试。
<center>
    <img src="assert/fastapi_retrieval.png" alt="alt text" width="500" height="auto">
</center>

# Tests单元测试说明
## 视频理解模型单元测试示例
1. 直接使用视频理解模型，(tests/test_understand.py)
    ~~~
    pytest -s -v tests/test_understand.py
    ~~~
    可以得到对'data/videos/001.mp4' 视频理解的结果，可以通过改变路径，实现对不同视频的理解。

2. 通过FastAPI使用视频理解模型，(tests/test_understand_api.py)
    ~~~
    uvicorn apps.Understand.understand_api:app --reload # 首先需要开启FastAPI服务
    pytest -s -v tests/test_understand_api.py # 执行单元测试
    ~~~
    具体使用方法详见：视频理解模型FastAPI使用。

3. 直接使用视频检索算法，(tests/test_retrieval.py)
    ~~~
    pytest -s -v tests/test_retrieval.py
    ~~~
    执行后，可以得到向量检索结果和大语言模型深度检索结果。

4. 通过FastAPI使用视频理解模型，(tests/test_understand_api.py)
    ~~~
    uvicorn apps.Retrieval.retrieval_api:app --reload # 首先需要开启FastAPI服务
    pytest -s -v tests/test_retrieval_api.py # 调用单元测试模块
    ~~~
    具体使用方法详见：检索算法FastAPI使用。
