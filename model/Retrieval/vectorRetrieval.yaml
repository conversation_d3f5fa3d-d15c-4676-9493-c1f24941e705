# api for embedding model：
dense_embedding_type: "api"

# huggingface 
# dense_embedding_type: "huggingface"

collection_name: "midexamine_bash"

use_pre_process: False
pre_process_cfg:
    model: "gpt-4o"
    temperature: 0.0
    top_p: 1.0
    max_tokens: 1000
    max_try_count: 3
    pre_process_prompt:
        system: "您是一个文本扩充专家。您将对用户输入的意图进行解读，并对其进行扩充，使其更容易检索到所需要的视频片段。"
        user: |
            "请对用户输入的意图进行扩充，使其更容易检索到所需要的视频片段。需要扩充的意图如下：{query}。
            请注意，输出格式为：视频展示了...
            要求如下：\n"
            "1. 保证描述完整性，合理地补充用户描述中缺失的细节。\n"
            "2. 尽量突出主体对象。\n"
            "3. 不要添加任何不相关的内容。"
            "4. 输出格式为：视频展示了..."
            "5. 输出字数在50字左右。"
            "6. 输出结果为纯文本，不要包含任何的markdown格式。"
            "7. 不要添加任何的解释说明。"


pre_limit: 10
limit: 10

index_params: {
    "index_type": "IVF_FLAT",  # 可以选择其他类型，如 IVF_SQ8, HNSW 等
    "metric_type": "L2",      # 可以是 L2 或 IP（内积）
    "params": {"nlist": 128}  # nlist 决定聚类中心数量}
}

fields:
    raw_caption: 
        dtype: "dense_vector"
        str_kwargs: 
            max_length: 65_535
    poi: 
        dtype: "dense_vector"
        str_kwargs: 
            max_length: 65_535
    coarse_attributes:
        dtype: "dense_vector"
        str_kwargs: 
            max_length: 65_535
    fine_attributes:
        dtype: "dense_vector"
        str_kwargs:
            max_length: 65_535
    video_path:
        dtype: "str"
        max_length: 65_535
    start_time:
        dtype: "float"
    end_time:
        dtype: "float"
    start_frame:
        dtype: "float"
    end_frame:
        dtype: "float"
    attribute_caption_useless: 
        dtype: "str"
        max_length: 65_535
    highlight:
        dtype: "float"
    

meta_fields: ["video_path", "start_time", "end_time"]
return_fields: ["video_path", "start_frame", "end_frame", "start_time", "end_time", "raw_caption", "coarse_attributes", "fine_attributes", "poi", "attribute_caption_useless", "highlight"]

index_fields: ["raw_caption_vector", 
               "poi_vector", 
               "coarse_attributes_vector", 
               "fine_attributes_vector"]

weight_list: {"raw_caption_vector": 1.0,
              "coarse_attributes_vector": .5, 
              "fine_attributes_vector": .5} 

enable_dynamic_field: False
primary_field: "pk"

drop_old: False
