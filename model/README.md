# 视频理解模型和视频检索算法说明
本项目包含视频理解模型和视频检索算法的配置文件，旨在提供高效的视频内容分析和检索功能。以下是各个文件和文件夹的详细说明：
## model 文件夹结构
```plaintext
model/
├── Retrieval/
│   ├── llmRetrieval_prompt.yaml # 大语言模型深度检索时使用的prompt
│   ├── llmRetrieval.yaml       # 大语言模型深度检索时使用的配置文件
│   └── vectorRetrieval.yaml    # 向量检索时使用的配置文件
└── Understand/
    ├── understand_prompt.yaml # 视频理解模型使用的prompt
    └── understand.yaml         # 视频理解模型使用的配置文件
└── README.md
```

## 视频理解模型和视频检索算法的推理方式

本项目提出的视频理解模型和视频检索算法都不需要训练，只需要推理即可。

### 视频理解

1. **视频理解配置文件说明** (model/Understand/understand.yaml)
   - **llm_type: ENNVL**
     - 描述：指定使用的大语言模型（LLM）类型。`ENNVL`表示项目远程部署的视频理解模型，调用API用于视频理解任务。
   - **model**: 指定使用的预训练模型名称。
   - **sample_rate: 1**
     - 描述：指定采样率，用于控制从视频中提取帧的频率。值为1表示每帧都进行处理。
   - **max_num: 1**
     - 描述：指定最大采样数量，即从视频中提取的最大帧数或特征数。值为1表示只处理一帧或一个特征。
   - **sample_type: uniform**
     - 描述：指定采样类型，`uniform`表示均匀采样，即从视频中均匀地选择帧或特征。
   - **sample_size: 5**
     - 描述：指定采样大小，即每次采样操作中选取的帧或特征的数量。值为5表示每次采样5个帧或特征。
   - **temperature: 0**
     - 描述：控制生成文本的随机性。值越高，生成的文本越随机；值越低，生成的文本越确定。值为0表示生成的文本完全确定。
   - **top_k: 1**
     - 描述：在生成文本时，只考虑概率最高的k个词。值为1表示只考虑概率最高的词。
   - **top_p: 1.0**
     - 描述：在生成文本时，只考虑累积概率达到p的词。值为1.0表示考虑所有词。
   - **do_sample: False**
     - 描述：是否进行采样。`False`表示不进行采样，生成的文本完全确定。
   - **num_beams: 1**
     - 描述：在生成文本时，使用的beam搜索的beam数量。值为1表示使用单beam搜索。
   - **extract_audio: False**
     - 描述：是否从视频中提取音频信息。`False`表示不提取音频。
   - **detect_image: True**
     - 描述：是否从视频中检测图像信息。`True`表示检测图像。
   - **prompt: model/Understand/understand_prompt.yaml**
     - 描述：指定用于理解任务的提示文件路径。该文件包含用于指导模型理解视频内容的提示信息。

2. **Prompt文件说明** (model/Understand/understand_prompt.yaml)
   该文件定义了理解模型的prompt流程，用于指导模型逐步完成视频片段的分析，并生成适用于文本驱动的向量化视频检索系统的描述和标签。以下是各个阶段的详细说明：

   - **场景分析**
     - 输入：视频片段（连续图像帧，主体用红色矩形框框出）及音频信息（可能为空）。
     - 目标：确定视频片段的具体场景类型。
     - 输出：场景类型的中文短语。
   - **场景相关辅助信息提取**
     - 输入：阶段一的场景类型短语及视频片段。
     - 目标：提取有助于理解场景的详细辅助信息。
     - 输出：与场景高度相关的辅助信息短语。
   - **视频内容描述**
     - 输入：阶段一场景类型、阶段二辅助信息及视频片段。
     - 目标：生成适用于向量检索的主要内容描述。
     - 输出：精炼的视频内容描述。
   - **粗粒度标签生成**
     - 输入：阶段三的视频内容描述。
     - 目标：提炼适合粗筛的高层语义标签。
     - 输出：粗粒度标签列表。
   - **细粒度标签生成**
     - 输入：阶段三的视频内容描述及阶段四生成的粗粒度标签。
     - 目标：生成更具体的细粒度标签。
     - 输出：细粒度标签列表。
     
    ---
   以上是`model/Understand/understand_prompt.yaml`文件的详细说明，它确保所有输出精准且结构清晰，完全适用于高效的文本驱动向量视频检索任务。

### 视频检索

本项目视频检索包含三个主要的YAML配置文件，用于定义向量检索和大预言模型深度检索。以下是每个文件的简要说明：

#### 1. vectorRetrieval.yaml

此文件用于配置向量检索模型的相关参数。它定义了如何使用嵌入模型（embedding model）来生成视频内容的向量表示，并据此进行视频检索。

- **dense_embedding_type**: 指定嵌入模型的类型，例如使用API或Hugging Face模型。
- **collection_name**: 指定用于检索的数据集合名称。
- **use_pre_process**: 指示是否使用预处理步骤来扩充用户查询。
- **pre_process_cfg**: 包含预处理的详细配置，如使用的模型、温度参数等。
- **index_params**: 定义索引参数，如索引类型、度量类型和参数。
- **fields**: 列出所有用于检索的字段及其数据类型。
- **meta_fields**: 指定元数据字段，这些字段不参与向量检索，但会返回给用户。
- **return_fields**: 指定检索时返回的字段列表。
- **index_fields**: 指定用于建立索引的字段。
- **weight_list**: 为不同字段设置权重，影响检索结果的排序。
- **enable_dynamic_field**: 指示是否启用动态字段。
- **primary_field**: 指定主键字段。
- **drop_old**: 指示是否删除旧索引。

#### 2. llmRetrieval.yaml

此文件配置了基于大型语言模型（LLM）的检索系统。它定义了如何使用LLM来处理和响应用户的查询。

- **model**: 指定使用的LLM模型。
- **max_tokens**: 设置生成文本的最大token数。
- **max_try_count**: 设置最大尝试次数。
- **empty_caption**: 定义空描述的占位符。
- **sample_rate**: 设置采样率。
- **temperature**: 控制生成文本的随机性。
- **top_p**: 设置核采样的阈值。
- **topk**: 指定返回的顶级结果数量。
- **retrieval_keys**: 列出用于检索的关键字。
- **retrieval_prompt**: 指定检索提示文件的路径。
- **llm_type**: 指定LLM的类型。

#### 3. llmRetrieval_prompt.yaml

此文件定义了用于LLM检索的提示（prompt）。它指导模型如何理解和处理用户的查询，并生成相关的检索结果。

- **prompt**: 包含系统和用户的提示文本。
  - **system**: 定义系统的角色和任务目标，包括如何处理视频片段的结构化描述和用户的查询指令。
  - **user**: 定义用户的查询指令和视频片段列表的格式。

这些配置文件共同工作，以实现一个高效的视频检索系统，该系统能够理解和响应用户的查询，并返回最相关的视频片段。

---

以上是三个配置文件的简要说明。正确配置这些文件对于模型的性能和输出结果至关重要。请根据具体需求调整这些参数，以获得最佳效果。
