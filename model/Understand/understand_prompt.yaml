system: |
  您是一位专业的视频分析专家，擅长对连续图像帧形式的视频内容进行深入理解与结构化文本表达。您生成的所有文本描述及标签，将直接用于文本驱动的向量化视频检索系统。因此，您的输出必须具备高语义准确性、丰富的关键词密度和明确的语义区分度，以确保高效精准的向量检索效果。

  请注意，为了方便您理解，我们将视频片段中的主体用红色矩形框框出，并提供音频信息（通过音频工具提取，可能为空）。您可以结合这些辅助信息，更好地理解视频片段。

  您将按照以下五个阶段逐步完成视频片段分析，每个阶段输出将作为下一阶段的关键输入，务必严格遵循每阶段的输入、目标及输出规范。在每个阶段，您都需要进行以下思考过程：
  1. 仔细分析输入信息，识别关键要素
  2. 建立要素间的关联关系
  3. 提取核心特征和模式
  4. 验证信息的完整性和一致性
  5. 生成符合要求的输出

  1. 场景分析
  - **输入**：视频片段（即连续图像帧，每张图片已经将主体用红色矩形框框出）及音频信息（通过音频工具提取，可能为空）
  - **目标**：确定视频片段的具体场景类型。
  - **思考过程**：
    * 分析场景的空间布局和物理特征
    * 识别场景中的关键物体和元素
    * 理解场景的功能和用途
    * 确定场景的具体类型和子类别

  2. 场景相关辅助信息提取
  - **输入**：阶段一的场景类型短语及视频片段。
  - **目标**：提取有助于理解场景的详细辅助信息。
  - **思考过程**：
    * 分析场景中的动态元素
    * 识别场景中的静态特征
    * 理解场景中的时间特征
    * 提取场景中的环境信息

  3. 视频内容描述
  - **输入**：阶段一场景类型、阶段二辅助信息及视频片段。
  - **目标**：生成适用于向量检索的主要内容描述。
  - **思考过程**：
    * 整合场景类型和辅助信息
    * 分析场景中的主要活动
    * 理解场景中的互动关系
    * 提取场景中的关键事件

  4. 粗粒度标签生成
  - **输入**：阶段三的视频内容描述。
  - **目标**：提炼适合粗筛的高层语义标签。
  - **思考过程**：
    * 分析内容描述中的核心要素
    * 识别主要对象和动作
    * 理解场景的时空特征
    * 提取场景的情感基调

  5. 细粒度标签生成
  - **输入**：阶段三的视频内容描述及阶段四生成的粗粒度标签。
  - **目标**：生成更具体的细粒度标签。
  - **思考过程**：
    * 分析粗粒度标签的具体表现
    * 识别场景中的细节特征
    * 理解场景中的具体元素
    * 提取场景中的独特属性
  
  请严格遵循上述各阶段规范，确保所有输出精准且结构清晰，完全适用于高效的文本驱动向量视频检索任务。 注意，请不要返回空字符串。

final_output_variables: ["scene_type", "scene_info", "raw_caption", "coarse_attributes", "fine_attributes"]

users: 
  begin: 
    type: variable
    names: ["scene_types", "num_scenes"]
    values: [[], 0]
    next: set_audio_info
  end: 
    type: end
  set_audio_info:
    type: set_variables
    names: ["audio_info"]
    next: check_scenes
  check_scenes:
    type: gate
    input_variables: ["num_scenes"]
    expression: "{} == 0"
    true_next: common_scene_indentify
    false_next: specific_scene_indentify
  common_scene_indentify:
    type: prompt
    input_variables: ["audio_info"]
    main_text: |
      请对以下视频片段进行场景类型识别。
      视频片段中已经将主体用红色矩形框框出，并提供音频信息（通过音频工具提取，可能为空）。
      {}。

      该视频展示的是一个具有"旅游"属性的场景，但请仅依据视频片段中实际呈现的视觉内容，判断该视频所处的具体场景类型（例如：城市街道、海边栈道、寺庙庭院、山间小路、室内博物馆等）。

      # 分析步骤：
      1. 观察场景的空间布局和物理特征
      2. 识别场景中的关键物体和元素
      3. 理解场景的功能和用途
      4. 确定场景的具体类型和子类别

      # 输出目标：
      - 输出一个能够准确概括该视频片段场景类型的中文短语。

      # 输出指导：
      1. 请仅基于图像帧中可见的内容进行判断，不添加任何主观猜测或假设信息。
      2. 不使用模糊或缺乏区分度的描述（如"普通旅游地"、"常见户外"）。
      3. 场景类型应体现空间位置、环境特征或背景布局，而非事件或情绪。
      4. 输出内容必须是10个中文词语以内的短语，简洁明确。
      5. **仅输出短语本身，不包含句子、解释或额外说明。**

      # 示例输出（不可照抄，仅供理解风格）：
      - 城市街头夜景  
      - 海边观景栈道  
      - 山区徒步小道  
      - 古建筑庭院
    output_variables: ["scene_type"]
    next: common_scene_knowledge
  common_scene_knowledge:
    type: prompt
    input_variables: ["scene_type", "audio_info"]
    main_text: |
      视频片段中已经将主体用红色矩形框框出，并提供音频信息（通过音频工具提取）。
      {}。

      该视频片段展示了"{}"的场景。请基于视频画面中的真实内容，提取与该场景高度相关的辅助信息，用于支持后续的视频内容理解与标签生成任务。

      # 输出目标：
      提取能够增强对该场景理解的关键辅助信息，内容可包括典型人物/物体/行为、结构布局、时间特征、环境属性等。

      # 输出要求：
      1. 所有信息必须直接基于画面中可见内容，不得引入假设或常识补全。
      2. 辅助信息必须与场景语义强相关，能够帮助后续任务更准确地描述视频主内容。
      3. 不允许出现否定、模糊、推测性语言。
      4. 每条辅助信息限定在20个中文词语以内，语言需简洁明确。
      5. **仅输出辅助信息短语，不输出完整句子或任何额外说明。**
      6. 请仅输出下列场景中的一种：["航拍", "景点", "餐饮", "住宿", "旅游"]

      # 输出格式举例（仅供理解）：
      - 游客穿便装拍照打卡  
      - 景区标志性石碑和雕塑  
      - 白天日照强烈，无阴影  
      - 海边有凉亭与椰子树  
      - 商场内部人流密集、灯光明亮  
      - 夜间街道有车辆与霓虹灯
    output_variables: ["scene_info"]
    next: raw_caption
  specific_scene_indentify:
    type: prompt
    input_variables: ["num_scenes", "scene_types", "audio_info"]
    main_text: ""
    output_variables: ["scene_type"]
    next: check_specific_scene
  check_specific_scene:
    type: gate
    input_variables: ["scene_type"]
    expression: "{} == -1"
    true_next: common_scene_indentify
    false_next: specific_scene_knowledge
  specific_scene_knowledge:
    type: prompt
    input_variables: ["scene_types", "audio_info"]
    main_text: ""
    output_variables: ["scene_info"]
    next: raw_caption
  raw_caption: 
    type: prompt
    input_variables: ["scene_type", "scene_info", "audio_info"]
    main_text: |
      该视频片段属于"{}"场景，相关辅助信息如下："{}"。
      视频片段中已经将主体用红色矩形框框出，并提供音频信息（通过音频工具提取）。
      {}。

      # 分析步骤：
      1. 整合场景类型和辅助信息
      2. 分析场景中的主要活动
      3. 理解场景中的互动关系
      4. 提取场景中的关键事件

      请结合上述场景类型与辅助信息，生成一段对该视频片段主要内容的精炼描述，要求完整表达视频的核心内容与关键要素，突出其中涉及的人物、动作、物体、环境或事件等重要信息。

      # 输出要求：
      1. 所有描述必须仅依据视频本身与提供的辅助信息，严禁添加主观猜测、虚构细节或影视化叙述。
      2. 不进行画面逐帧转写，不使用"画面中看到""可以看到"等表达方式。
      3. 不使用模糊、否定、假设、主观感受或不确定性语言。
      4. 语言应客观、中性、逻辑清晰，突出语义信息与关键词密度。
      5. **描述长度不少于30个中文词语。**
      6. **仅输出描述内容本身，不添加解释、格式标注或提示语。**

      # 示例输出（不可照抄，仅供理解风格）：
      - 多名游客在古建筑前合影留念，环境开阔，人流较多，天气晴朗，建筑具有明显传统风格，现场氛围较为热闹。
      - 夜晚城市街头人车混行，路面潮湿反光，行人穿着轻便，车辆灯光频繁闪烁，街道两侧商铺林立、霓虹灯鲜艳。
    output_variables: ["raw_caption"]
    next: coarse_attributes
  coarse_attributes: 
    type: prompt
    input_variables: ["scene_type", "scene_info", "raw_caption", "audio_info"]
    main_text: |
      {}。
      该视频片段属于"{}"场景，其相关的辅助信息如下："{}"。该视频的具体描述如下："{}"。

      # 分析步骤：
      1. 分析内容描述中的核心要素
      2. 识别主要对象和动作
      3. 理解场景的时空特征
      4. 提取场景的情感基调

      请根据上述信息，从以下七个语义角度为该视频片段生成**粗粒度标签**。标签需以"词语或短语"方式表达，不得使用完整句子或解释性说明。

      # 七个语义角度（推荐）：
      1. 主体对象：视频中最核心的元素，如人物、动物、建筑、交通工具、道具等。
      2. 主体数量：场景中出现的主要对象数量，如"单人""两人""多人""大量人群"等。
      3. 关键行为/事件：主体正在执行的主要动作，或场景中发生的显著事件。
      4. 空间场所：视频中所处的空间环境类型，尽量具体化，不使用抽象词（如"户外"）。
      5. 时间特征：自然时间段或社会事件时间，如"白天""傍晚""节日""冬季"等。
      6. 互动关系/社交结构：人物间是否有交互、情境关系，如"家庭聚会""陌生路人""师生互动"等。
      7. 情绪氛围/情感基调：整体情绪倾向，如"轻松""紧张""喜庆""平静""压抑"等。

      # 输出要求：
      - 所有标签必须依据视频内容和辅助信息，不得臆测。
      - 标签形式为"词语或短语"，不超过8个汉字。
      - 标签需逻辑清晰、颗粒度一致，不可模糊泛化。
      - 严格按以下格式输出：  
      ["主体对象", "主体数量", "关键行为/事件", "空间场所", "时间特征", "互动关系/社交结构", "情绪氛围/情感基调"]

      # 输出示例：
      ["学生", "多人", "排队买餐", "高校食堂", "中午", "同学互动", "热闹"]
      ["游客", "两人", "合影拍照", "古镇桥边", "傍晚", "情侣关系", "温馨"]
    output_variables: ["coarse_attributes"]
    next: fine_attributes
  fine_attributes: 
    type: prompt
    input_variables: ["scene_type", "scene_info", "raw_caption", "coarse_attributes", "audio_info"]
    main_text: |
      {}。
      视频片段属于"{}"场景，其相关的辅助信息如下："{}"。  
      该视频的具体描述如下："{}"。  
      该视频的粗粒度标签如下："{}"。

      # 分析步骤：
      1. 分析粗粒度标签的具体表现
      2. 识别场景中的细节特征
      3. 理解场景中的具体元素
      4. 提取场景中的独特属性

      请根据以上信息，为该视频片段生成10个能够精细化概括视频内容的细粒度标签。这些标签必须是对上述粗粒度标签的深入拓展，并需涵盖视频内容中的具体元素、特征或细节。

      # 细粒度标签的具体要求：
      1. **数量限定**：精确提供10个细粒度标签。
      2. **具体拓展性**：每个细粒度标签必须是粗粒度标签的细化与延伸，不得直接重复粗粒度标签。  
        - 例如，粗粒度标签为「黄昏」，细粒度标签可为「日落时分」、「橙红色天空」。
      3. **多维度覆盖**：标签应从多个维度具体描述视频，如人物外貌特征、物体具体属性、环境细节、具体动作细节、情感表现、颜色特征、空间布局、氛围描绘等。
      4. **准确客观**：必须仅依据视频实际内容和所提供的辅助信息生成，不得添加任何假设或主观推测的信息。

      # 回答时注意：
      - 禁止逐一描述具体画面细节，不使用"画面出现"或"可以看到"等表述。
      - 不得使用否定、推测、不确定或模糊性语言（例如"不明显"、"可能"等）。

      # 输出格式严格规范如下：
      ["标签1", "标签2", "标签3", "标签4", "标签5", "标签6", "标签7", "标签8", "标签9", "标签10"]

      # 示例（仅供参考风格，不可照抄）：
      假设粗粒度标签为：  
      ["咖啡馆", "下午", "顾客互动", "悠闲", "饮品"]

      对应的细粒度标签示例：  
      ["木质装饰风格", "温暖柔和灯光", "三五好友聊天", "悠闲放松氛围", "年轻顾客群体", "下午阳光透窗", "奶泡拉花咖啡", "安静背景音乐", "书架装饰摆设", "桌面精致甜品"]

      请依据上述标准严格执行，确保生成的细粒度标签具体明确、易于检索且高质量。
    output_variables: ["fine_attributes"]
    next: end

